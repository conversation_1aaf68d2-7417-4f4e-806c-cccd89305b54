# 动态生成式"跑团"/冒险日志 (Adventure Log v3)

## 📋 项目概述

本项目是为 SillyTavern 开发的动态生成式文字冒险（跑团）界面。玩家扮演冒险者，在AI实时描述的奇幻世界中探索、互动并经历独特故事。AI扮演地下城主（DM）角色，根据玩家选择和世界书规则，动态生成场景描述、事件、NPC对话和行动选项。

### 🎯 核心特性

- ✅ **沉浸式AI驱动体验**：完全由AI生成的动态故事情节
- ✅ **完整法术系统**：支持300+法术，从世界书动态加载
- ✅ **智能战斗机制**：本地骰子检定，AI处理战斗逻辑
- ✅ **高性能缓存**：localStorage缓存机制，快速重新加载
- ✅ **模块化架构**：TypeScript模块化设计，易于维护扩展
- ✅ **响应式界面**：适配移动端和桌面端的优雅布局

## 🏗️ 项目结构

```
src/adventure_log_v3/
├── index.html              # 主界面文件
├── index.ts                # 主入口和应用逻辑
├── index.scss              # 样式文件
├── app.ts                  # 应用初始化
├── core/
│   └── state.ts            # 游戏状态管理
├── ui/
│   ├── domElements.ts      # DOM元素管理
│   └── render.ts           # UI渲染逻辑
├── spells/
│   └── index.ts            # 法术系统
├── combat/
│   └── index.ts            # 战斗系统
├── utils/
│   └── index.ts            # 工具函数
└── types/
    └── index.ts            # TypeScript类型定义
```

## 🎮 核心玩法

### 1. 角色管理
- **属性系统**：力量、敏捷、体质、智力、感知、魅力
- **生命值/魔法值**：动态HP/MP管理
- **装备系统**：武器、护甲、物品管理
- **法术书**：已装备法术的管理和施放

### 2. 场景探索
- **动态场景**：AI生成的环境描述和事件
- **NPC互动**：智能对话和关系系统
- **选择驱动**：玩家选择影响故事发展

### 3. 战斗系统
- **本地检定**：客户端处理骰子投掷和计算
- **法术施放**：目标选择和效果计算
- **武器攻击**：攻击检定和伤害计算

## 🔧 技术架构

### 数据流
```
用户操作 → 本地检定 → AI请求 → JSON解析 → 状态更新 → UI渲染
```

### 核心模块

#### 🎲 检定系统 (`utils/index.ts`)
- 自动识别检定格式：`[DC15 力量(运动)]`
- 本地1d20投掷和修正值计算
- 成功/失败/重击判定

#### 🔮 法术系统 (`spells/index.ts`)
- **世界书加载**：从`Spell_Library.json`动态加载
- **缓存机制**：localStorage缓存，避免重复加载
- **目标选择**：智能NPC检测和目标选择UI
- **效果计算**：伤害、治疗、状态效果

#### ⚔️ 战斗系统 (`combat/index.ts`)
- **武器攻击**：攻击检定和伤害计算
- **防御计算**：AC和豁免检定
- **状态管理**：生命值、状态效果追踪

## 📊 数据格式

### AI响应格式
```
查看系统
##@@_ADVENTURE_BEGIN_@@##
{
  "sceneType": "dialogue|location|combat|system_message|puzzle",
  "sceneTitle": "场景标题",
  "currentLocation": "当前地点",
  "time": "游戏内时间",
  "narrative": [
    {
      "type": "description|dialogue|system",
      "content": "叙述内容（使用\\n换行）"
    }
  ],
  "playerChoices": [
    {
      "id": "A",
      "text": "选择文本[DC15 属性(技能)]",
      "actionCommand": "action_command"
    }
  ],
  "enemies": [...],  // 战斗场景
  "variableUpdates": {...}  // 状态更新
}
##@@_ADVENTURE_END_@@##
关闭系统
```

### 玩家状态结构
```typescript
interface PlayerState {
  // 基础属性
  attributes: {
    strength: number;
    dexterity: number;
    constitution: number;
    intelligence: number;
    wisdom: number;
    charisma: number;
  };
  
  // 生命值和魔法值
  hitPoints: { current: number; max: number };
  magicPoints: { current: number; max: number };
  
  // 装备和物品
  equippedWeapons: WeaponTemplate[];
  equippedSpells: SpellTemplate[];
  inventory: InventoryItem[];
  
  // 技能和熟练项
  skills: Record<string, number>;
  proficiencies: string[];
}
```

## 🌟 法术系统详解

### 世界书配置
创建`Spell_Library.json`世界书，包含以下关键字之一：
- `AI_ALL_SPELLS` - 完整法术库（推荐）
- `AI_LEVEL_X_SPELLS` - 按等级分组（X为0-9）

### 缓存机制
```typescript
// 首次加载：从世界书加载并缓存
const SPELL_CACHE_KEY = 'spellTemplates_v1.0';
localStorage.setItem(SPELL_CACHE_KEY, JSON.stringify(spells));

// 后续加载：直接从缓存读取
const cachedSpells = localStorage.getItem(SPELL_CACHE_KEY);
```

### 调试命令
```javascript
// 重新加载法术
window.spellSystem.reloadSpellTemplates()

// 清除缓存并重新加载
window.spellSystem.clearSpellCache()

// 检查法术数量
window.spellSystem.getSpellTemplates().length
```

## 🔄 持久化机制

### 当前状态
- **玩家状态**：保存到宿主消息的HTML注释中
- **当前场景**：最新AI回复保存到宿主消息

### 历史记录
- **世界书存储**：每个AI回复保存为`dndRPG_history.json`中的独立条目
- **自动激活**：新条目自动设置`constant: true`，加入AI上下文

## 🚀 部署和使用

### 环境要求
- SillyTavern环境
- 支持ES6+的现代浏览器
- 世界书功能启用

### 安装步骤
1. 将项目文件部署到SillyTavern
2. 导入必要的世界书文件
3. 在SillyTavern中加载界面

### 世界书文件
- `Spell_Library.json` - 法术数据库
- `RPG_Modules_Test.json` - 玩家模板
- `dndRPG_history.json` - 游戏历史（自动创建）

## 🐛 故障排除

### 常见问题

#### 法术加载失败
- 检查世界书文件名：`Spell_Library.json`
- 确认关键字：`AI_ALL_SPELLS`
- 验证JSON格式正确性

#### 性能问题
- 清除法术缓存：`window.spellSystem.clearSpellCache()`
- 检查localStorage空间
- 考虑分级加载大型法术库

#### AI响应解析错误
- 检查AI回复格式
- 确认标记完整性：`##@@_ADVENTURE_BEGIN_@@##`
- 查看控制台错误信息

### 调试工具
```javascript
// 全局调试对象
window.spellSystem = {
  getSpellTemplates: () => SpellTemplate[],
  reloadSpellTemplates: () => Promise<void>,
  clearSpellCache: () => Promise<void>,
  testLorebookConnection: () => Promise<void>,
  testSpecificKey: (fileName?, key?) => Promise<void>
}
```

## 📈 性能优化

### 缓存策略
- **法术数据**：localStorage缓存，版本控制
- **DOM操作**：批量更新，避免重复渲染
- **AI请求**：合并状态更新，减少请求频率

### 内存管理
- **模块化加载**：按需加载功能模块
- **数据清理**：定期清理过期缓存
- **事件监听**：及时移除无用监听器

## 🔮 未来规划

### 短期目标
- [ ] 更多法术效果实现
- [ ] 战斗动画效果
- [ ] 音效和背景音乐

### 长期目标
- [ ] 多人游戏支持
- [ ] 自定义规则系统
- [ ] 可视化地图系统
- [ ] 角色创建向导

---

## 📝 更新日志

### v3.0 (当前版本)
- ✅ 完整重构法术系统
- ✅ 实现localStorage缓存机制
- ✅ 移除所有硬编码法术数据
- ✅ 优化性能和用户体验
- ✅ 完善文档和调试工具

### v2.x
- 基础法术系统实现
- 战斗机制开发
- UI界面优化

### v1.x
- 项目初始化
- 基础架构搭建
- AI交互机制
