import {
  SpellTemplate,
  SpellDamageResult,
  PlayerState,
  CheckResult,
} from '../types';
import {
  parseDamageString,
  rollDice,
  getProficiencyBonus,
  safeToastr,
} from '../utils';

// SillyTavern global functions
declare function triggerSlash(command: string): Promise<string | undefined>;

// 全局法术模板存储
let spellTemplates: SpellTemplate[] = [];

/**
 * 导出法术模板数组（只读）
 */
export function getSpellTemplates(): SpellTemplate[] {
  return spellTemplates;
}

/**
 * 手动重新加载法术模板（用于调试和测试）
 */
export async function reloadSpellTemplates(): Promise<void> {
  await loadSpellTemplates();
}

/**
 * 清除法术缓存并重新加载（用于更新世界书后）
 */
export async function clearSpellCache(): Promise<void> {
  localStorage.removeItem(SPELL_CACHE_KEY);
  safeToastr('info', '🗑️ 已清除法术缓存', '法术系统');
  await loadSpellsFromWorldBook();
}

/**
 * 简单的世界书连接测试（只用toastr调试）
 */
export async function testSpecificKey(fileName: string = 'Spell_Library.json', key: string = 'TEST_SPELL'): Promise<void> {
  safeToastr('info', `开始测试世界书: ${fileName}`, '世界书测试');

  if (typeof triggerSlash !== 'function') {
    safeToastr('error', 'triggerSlash 函数不可用', '世界书测试');
    return;
  }

  try {
    // 第一步：测试能否连接到世界书文件
    const findEntryCommand = `/findentry file="${fileName}" "${key}"`;
    safeToastr('info', `执行命令: ${findEntryCommand}`, '世界书测试');

    const uidResult = await triggerSlash(findEntryCommand);

    if (uidResult === undefined) {
      safeToastr('error', `❌ 世界书文件 ${fileName} 不存在或无法访问`, '世界书测试');
      return;
    }

    if (uidResult.trim() === '' || uidResult.trim() === '[]') {
      safeToastr('warning', `✅ 世界书 ${fileName} 存在，但未找到关键字 ${key}`, '世界书测试');
      return;
    }

    // 第二步：尝试解析UID
    let entryUid: string | null = null;
    try {
      // 尝试解析为JSON
      const parsedUidResult = JSON.parse(uidResult);
      if (Array.isArray(parsedUidResult) && parsedUidResult.length > 0) {
        // 标准数组格式: ["12345"]
        entryUid = parsedUidResult[0].toString();
      } else {
        // 直接数字或字符串格式: 6000
        entryUid = parsedUidResult.toString();
      }
    } catch (e) {
      // 如果JSON解析失败，直接使用原始结果作为UID
      entryUid = uidResult.trim();
    }

    if (entryUid) {
      // 第三步：尝试获取内容
      const getContentCommand = `/getentryfield file="${fileName}" field=content "${entryUid}"`;
      const content = await triggerSlash(getContentCommand);

      if (content && content.trim() !== '') {
        safeToastr('success', `✅ 成功获取内容，长度: ${content.length} 字符`, '世界书测试');
      } else {
        safeToastr('warning', `⚠️ 条目存在但内容为空`, '世界书测试');
      }
    } else {
      safeToastr('error', `❌ 无法解析UID: ${uidResult}`, '世界书测试');
    }
  } catch (error) {
    safeToastr('error', `❌ 连接失败: ${(error as Error).message}`, '世界书测试');
  }
}

/**
 * 测试世界书连接（调试用）
 */
export async function testLorebookConnection(): Promise<void> {
  await testSpecificKey('Spell_Library.json', 'AI_ALL_SPELLS');
}

/**
 * 强制触发法术模板加载（调试用）
 */
export async function forceLoadSpells(): Promise<void> {
  safeToastr('info', '🚀 强制触发法术模板加载...', '调试测试');
  await loadSpellTemplates();
  safeToastr('info', `📊 当前法术模板数量: ${spellTemplates.length}`, '调试测试');
}

/**
 * 从世界书加载法术数据
 */
async function loadSpellDataFromLorebook(searchKey: string, fileName?: string): Promise<string | null> {
  if (typeof triggerSlash !== 'function') {
    return null;
  }

  // 只测试一个文件名
  const lorebookFileName = 'Spell_Library.json';

  try {
    const findEntryCommand = `/findentry file="${lorebookFileName}" "${searchKey}"`;
    const uidResult = await triggerSlash(findEntryCommand);

    if (uidResult === undefined) {
      // 只在检测世界书存在性时显示toastr
      if (searchKey === 'AI_ALL_SPELLS') {
        safeToastr('error', `❌ 世界书文件 ${lorebookFileName} 不存在`, '世界书检测');
      }
      return null;
    }

    if (!uidResult?.trim() || uidResult.trim() === '[]') {
      // 只在检测主要关键字时显示toastr
      if (searchKey === 'AI_ALL_SPELLS') {
        safeToastr('warning', `✅ 世界书 ${lorebookFileName} 存在，但未找到 ${searchKey}`, '世界书检测');
      }
      return null;
    }

    let entryUid: string | null = null;
    try {
      // 尝试解析为JSON
      const parsedUidResult = JSON.parse(uidResult);
      if (Array.isArray(parsedUidResult) && parsedUidResult.length > 0) {
        // 标准数组格式: ["12345"]
        entryUid = parsedUidResult[0].toString();
      } else {
        // 直接数字或字符串格式: 6000
        entryUid = parsedUidResult.toString();
      }
    } catch (e) {
      // 如果JSON解析失败，直接使用原始结果作为UID
      entryUid = uidResult.trim();
      if (searchKey === 'AI_ALL_SPELLS') {
        safeToastr('info', `🔧 使用原始UID: ${entryUid}`, '世界书检测');
      }
    }

    if (!entryUid) {
      if (searchKey === 'AI_ALL_SPELLS') {
        safeToastr('error', `❌ 无法解析UID: ${uidResult}`, '世界书检测');
      }
      return null;
    }

    const getContentCommand = `/getentryfield file="${lorebookFileName}" field=content "${entryUid}"`;
    const content = await triggerSlash(getContentCommand);

    if (content && content.trim() !== '') {
      // 只在成功获取主要内容时显示toastr
      if (searchKey === 'AI_ALL_SPELLS') {
        safeToastr('success', `✅ 成功从世界书获取 ${searchKey}`, '世界书检测');
      }
      return content;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
}

/**
 * 分级加载法术数据（备用方案）
 */
async function loadSpellsByLevel(): Promise<SpellTemplate[]> {
  const allSpells: SpellTemplate[] = [];
  const spellLevelKeys = [
    'AI_CANTRIPS',      // 戏法
    'AI_LEVEL_1_SPELLS', // 1环法术
    'AI_LEVEL_2_SPELLS', // 2环法术
    'AI_LEVEL_3_SPELLS', // 3环法术
    'AI_LEVEL_4_SPELLS', // 4环法术
    'AI_LEVEL_5_SPELLS', // 5环法术
    'AI_LEVEL_6_SPELLS', // 6环法术
    'AI_LEVEL_7_SPELLS', // 7环法术
    'AI_LEVEL_8_SPELLS', // 8环法术
    'AI_LEVEL_9_SPELLS'  // 9环法术
  ];

  for (const levelKey of spellLevelKeys) {
    try {
      const levelSpellData = await loadSpellDataFromLorebook(levelKey);
      if (levelSpellData) {
        const levelSpells: SpellTemplate[] = JSON.parse(levelSpellData);
        if (Array.isArray(levelSpells)) {
          allSpells.push(...levelSpells);
        }
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  return allSpells;
}

/**
 * 加载法术模板数据
 */
const SPELL_CACHE_VERSION = '1.0';
const SPELL_CACHE_KEY = `spellTemplates_v${SPELL_CACHE_VERSION}`;

export async function loadSpellTemplates(): Promise<void> {
  try {
    // 首先尝试从localStorage加载缓存
    const cachedSpells = localStorage.getItem(SPELL_CACHE_KEY);
    if (cachedSpells) {
      try {
        const loadedSpells: SpellTemplate[] = JSON.parse(cachedSpells);
        if (Array.isArray(loadedSpells) && loadedSpells.length > 0) {
          spellTemplates = loadedSpells;
          //safeToastr('info', `⚡ 从缓存快速加载 ${spellTemplates.length} 个法术`, '法术系统');
          return;
        }
      } catch (cacheError) {
        // 缓存损坏，清除并继续从世界书加载
        localStorage.removeItem(SPELL_CACHE_KEY);
      }
    }

    // 缓存不存在或无效，从世界书加载
    await loadSpellsFromWorldBook();

  } catch (error) {
    spellTemplates = getDefaultSpellTemplates();
    safeToastr('warning', `⚠️ 法术加载失败，使用默认模板`, '法术系统');
  }
}

/**
 * 从世界书加载法术并缓存
 */
async function loadSpellsFromWorldBook(): Promise<void> {
  // 方案1: 尝试从世界书加载完整的法术库
  const spellDataJson = await loadSpellDataFromLorebook('AI_ALL_SPELLS');

  if (spellDataJson) {
    try {
      const loadedSpells: SpellTemplate[] = JSON.parse(spellDataJson);
      if (Array.isArray(loadedSpells) && loadedSpells.length > 0) {
        spellTemplates = loadedSpells;
        // 缓存到localStorage
        localStorage.setItem(SPELL_CACHE_KEY, JSON.stringify(spellTemplates));
        safeToastr('success', `✅ 从世界书加载 ${spellTemplates.length} 个法术并已缓存`, '法术系统');
        return;
      }
    } catch (parseError) {
      // 静默处理解析错误
    }
  }

  // 方案2: 如果完整加载失败，尝试分级加载
  const spellsByLevel = await loadSpellsByLevel();

  if (spellsByLevel.length > 0) {
    spellTemplates = spellsByLevel;
    // 缓存到localStorage
    localStorage.setItem(SPELL_CACHE_KEY, JSON.stringify(spellTemplates));
    safeToastr('success', `✅ 分级加载 ${spellTemplates.length} 个法术并已缓存`, '法术系统');
    return;
  }

  // 方案3: 如果所有世界书加载都失败，使用默认法术模板
  spellTemplates = getDefaultSpellTemplates();
  safeToastr('info', `📋 使用默认法术模板，共 ${spellTemplates.length} 个法术`, '法术系统');
}

/**
 * 获取法术模板
 */
export function getSpellTemplate(spellName: string): SpellTemplate | null {
  if (!spellTemplates || spellTemplates.length === 0) {
    return null;
  }

  const template = spellTemplates.find(
    template => template.name_zh === spellName || template.name_en === spellName
  ) || null;

  return template;
}

/**
 * 获取所有法术模板
 */
export function getAllSpellTemplates(): SpellTemplate[] {
  return [...spellTemplates];
}

/**
 * 计算法术攻击检定
 */
export function performSpellAttack(
  spellName: string,
  targetAC: number,
  playerState: PlayerState,
  advantageState: 'advantage' | 'disadvantage' | 'normal' = 'normal'
): CheckResult {
  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    throw new Error(`未找到法术模板: ${spellName}`);
  }

  // 确定施法属性
  const castingAbility = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as keyof PlayerState['attributes'];
  const attributeMod = playerState.attributes[castingAbility]?.mod || 0;
  const proficiencyBonus = getProficiencyBonus(playerState.level);

  // 执行攻击检定
  let roll1 = Math.floor(Math.random() * 20) + 1;
  let roll = roll1;

  if (advantageState === 'advantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.max(roll1, roll2);
  } else if (advantageState === 'disadvantage') {
    const roll2 = Math.floor(Math.random() * 20) + 1;
    roll = Math.min(roll1, roll2);
  }

  const total = roll + attributeMod + proficiencyBonus;
  const success = total >= targetAC;
  const isCritical = roll === 20;
  const isFumble = roll === 1;

  return {
    success: isCritical || (!isFumble && success),
    roll,
    attributeMod,
    proficiencyBonusApplied: proficiencyBonus,
    total,
    dc: targetAC,
    attributeName: castingAbility,
    skillName: spellName,
    isCritical,
    isFumble,
    advantageState,
  };
}

/**
 * 计算法术伤害
 */
export function calculateSpellDamage(
  spellName: string,
  playerState: PlayerState,
  isCritical: boolean = false,
  spellSlotLevel?: number
): SpellDamageResult {
  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    throw new Error(`未找到法术模板: ${spellName}`);
  }

  if (!spellTemplate.damage) {
    return {
      totalDamage: 0,
      damageType: '无伤害',
      details: '此法术不造成伤害',
      breakdown: [],
    };
  }

  let totalDamage = 0;
  let damageBreakdown: { type: string; amount: number; source: string }[] = [];
  let detailsParts: string[] = [];
  let projectileCount = spellTemplate.num_projectiles || 1;

  // 获取施法属性调整值
  const castingAbility = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as keyof PlayerState['attributes'];
  const castingMod = playerState.attributes[castingAbility]?.mod || 0;

  // 处理戏法等级提升
  let baseDamage = spellTemplate.damage;
  if (spellTemplate.level === 0 && spellTemplate.scaling) {
    const playerLevel = playerState.level;
    for (const [levelStr, scaledDamage] of Object.entries(spellTemplate.scaling)) {
      const level = parseInt(levelStr);
      if (playerLevel >= level) {
        baseDamage = scaledDamage;
      }
    }
  }

  // 处理升环施法
  if (spellSlotLevel && spellSlotLevel > spellTemplate.level && spellTemplate.higher_level_cast) {
    const levelsAboveBase = spellSlotLevel - spellTemplate.level;
    const effect = spellTemplate.higher_level_cast.effect;
    
    if (effect.includes('增加') && effect.includes('d')) {
      // 处理伤害增加，如 "增加1d6伤害"
      const damageMatch = effect.match(/增加(\d+d\d+)/);
      if (damageMatch) {
        const additionalDamage = damageMatch[1];
        const parsedAdditional = parseDamageString(additionalDamage);
        if (parsedAdditional) {
          for (let i = 0; i < levelsAboveBase; i++) {
            let additionalRoll = rollDice(parsedAdditional.count, parsedAdditional.die);
            if (isCritical) {
              additionalRoll += rollDice(parsedAdditional.count, parsedAdditional.die);
            }
            totalDamage += additionalRoll;
            detailsParts.push(`升环+${i + 1}(${additionalDamage}): ${additionalRoll}`);
          }
        }
      }
    } else if (effect.includes('飞弹') || effect.includes('投射物')) {
      // 处理投射物增加，如魔法飞弹
      projectileCount += levelsAboveBase;
    }
  }

  // 计算基础伤害
  const parsedDamage = parseDamageString(baseDamage);
  if (parsedDamage) {
    for (let i = 0; i < projectileCount; i++) {
      let damageRoll = rollDice(parsedDamage.count, parsedDamage.die);
      
      if (isCritical) {
        damageRoll += rollDice(parsedDamage.count, parsedDamage.die);
      }
      
      damageRoll += parsedDamage.modifier;
      
      // 添加施法调整值（如果适用）
      if (spellTemplate.add_casting_modifier_to_damage) {
        damageRoll += castingMod;
      }
      
      totalDamage += damageRoll;
      
      if (projectileCount > 1) {
        detailsParts.push(`投射物${i + 1}: ${damageRoll}`);
      } else {
        detailsParts.push(`基础伤害: ${damageRoll}`);
      }
    }
    
    damageBreakdown.push({
      type: spellTemplate.damage_type || '魔法',
      amount: totalDamage,
      source: spellName,
    });
  }

  const detailsString = detailsParts.join(' + ') + ` = ${totalDamage}`;
  
  // 计算豁免DC（如果需要）
  let saveRequired: SpellDamageResult['saveRequired'];
  if (spellTemplate.save) {
    const saveDC = 8 + castingMod + getProficiencyBonus(playerState.level);
    saveRequired = {
      attribute: spellTemplate.save.attribute,
      dc: saveDC,
      effectOnSuccess: spellTemplate.save.effect_on_success,
    };
  }

  return {
    totalDamage,
    damageType: spellTemplate.damage_type || '魔法',
    details: detailsString,
    breakdown: damageBreakdown,
    projectileCount: projectileCount > 1 ? projectileCount : undefined,
    saveRequired,
  };
}

/**
 * 检查法术槽是否足够
 */
export function canCastSpell(spellName: string, playerState: PlayerState, spellSlotLevel?: number): boolean {
  //safeToastr('info', `检查法术施放能力: ${spellName}`, 'Debug - Can Cast');

  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    //safeToastr('error', `找不到法术模板: ${spellName}`, 'Debug - Can Cast');
    return false;
  }

  const requiredLevel = spellSlotLevel || spellTemplate.level;
  //safeToastr('info', `法术等级: ${requiredLevel}`, 'Debug - Can Cast');

  // 戏法不需要法术槽
  if (requiredLevel === 0) {
    //safeToastr('success', `${spellName} 是戏法，可以施放`, 'Debug - Can Cast');
    return true;
  }

  if (!playerState.spellSlots) {
    //safeToastr('error', '玩家没有法术槽数据', 'Debug - Can Cast');
    return false;
  }

  const slotInfo = playerState.spellSlots[requiredLevel.toString()];
  const canCast = slotInfo && slotInfo.current > 0;

  if (slotInfo) {
    //safeToastr('info', `${requiredLevel}环法术槽: ${slotInfo.current}/${slotInfo.max}`, 'Debug - Can Cast');
  } else {
    //safeToastr('error', `没有${requiredLevel}环法术槽`, 'Debug - Can Cast');
  }

  //safeToastr(canCast ? 'success' : 'error', `${spellName} ${canCast ? '可以' : '无法'}施放`, 'Debug - Can Cast');
  return canCast;
}

/**
 * 消耗法术槽
 */
export function consumeSpellSlot(playerState: PlayerState, spellSlotLevel: number): boolean {
  if (spellSlotLevel === 0) return true; // 戏法不消耗法术槽

  const slotInfo = playerState.spellSlots[spellSlotLevel.toString()];
  if (slotInfo && slotInfo.current > 0) {
    slotInfo.current--;
    return true;
  }
  return false;
}

/**
 * 获取默认法术模板（空数组，完全依赖世界书数据）
 */
function getDefaultSpellTemplates(): SpellTemplate[] {
  return [];
}

// 导出到全局作用域以便在控制台中使用
declare global {
  interface Window {
    spellSystem: {
      getSpellTemplates: () => SpellTemplate[];
      reloadSpellTemplates: () => Promise<void>;
      clearSpellCache: () => Promise<void>;
      testLorebookConnection: () => Promise<void>;
      testSpecificKey: (fileName?: string, key?: string) => Promise<void>;
    };
  }
}

// 将函数添加到全局作用域
if (typeof window !== 'undefined') {
  window.spellSystem = {
    getSpellTemplates,
    reloadSpellTemplates,
    clearSpellCache,
    testLorebookConnection,
    testSpecificKey,
  };
}
