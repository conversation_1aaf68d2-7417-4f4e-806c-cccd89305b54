# 冒险日志 v3 - 战斗逻辑更新计划

## 1. 引言

本文档概述了更新冒险日志 v3 战斗逻辑的计划，整合 D&D 5e 关于攻击检定、豁免检定、伤害和武器属性的规则。目标是参考提供的 D&D 5e 玩家手册 2024 摘录，增强战斗遭遇的准确性和深度。

## 2. 核心规则实施

### 2.1. 攻击检定

*   **计算**: `d20 + 属性调整值 + 熟练加值 (如果熟练) + 其他加值/减值`
*   **命中判定**: 结果 >= 目标 AC。
*   **属性调整值**:
    *   近战武器: 力量 (默认)，或敏捷（如果具有灵巧属性且敏捷 > 力量）。
    *   远程武器: 敏捷。
    *   法术攻击: 施法属性调整值 (由施法者职业/状态定义，例如法师为智力，牧师为感知，术士为魅力)。
    *   徒手打击: 力量。
*   **熟练加值**: 如果角色熟练所使用的武器，或者是由熟练的施法者进行的法术攻击，则应用此加值。武器熟练项在 `PlayerState.proficiencies` 中列出。
*   **天然 20**: 自动命中。这也是一次重击 (参见伤害掷骰)。
*   **天然 1**: 自动失手。
*   **所需数据 (`PlayerState`)**: `attributes` (获取调整值), `proficiencies` (获取武器熟练), `level` (计算熟练加值)。
*   **所需数据 (`EnemyStateJSON`)**: `ac`。
*   **AI 交互与客户端逻辑**:
    *   **玩家攻击**:
        1.  客户端识别使用的武器/法术和目标。
        2.  客户端确定相关属性（力量、敏捷或施法属性）并检查灵巧属性。
        3.  客户端检查武器熟练。
        4.  客户端进行 d20 掷骰，加上属性调整值和熟练加值 (如果适用)。
        5.  客户端将总值与目标的 AC (来自 `EnemyStateJSON`) 进行比较。
        6.  客户端告知 AI 掷骰结果、是否命中/失手，以及是否为天然 1 或天然 20。
    *   **敌人攻击 (AI 宣告掷骰)**:
        1.  AI 描述敌人的攻击并说明攻击检定结果 (例如：“地精挥舞弯刀，掷出 18 点攻击！”)。
        2.  客户端将此掷骰与玩家的 AC 进行比较。
        3.  客户端告知 AI 攻击是否命中。
    *   或者，AI 可以提供其攻击加值，由客户端为敌人掷 d20。(初步计划：AI 宣告其总攻击检定结果)。

### 2.2. 豁免检定

*   **触发**: 通常由法术或特殊能力（来自玩家或敌人）触发，这些效应会指明需要进行豁免检定。
*   **计算**: `d20 + 属性调整值 + 熟练加值 (如果熟练该豁免) + 其他加值/减值`
*   **成功判定**: 结果 >= 效应的豁免 DC。
*   **属性调整值**: 由效应决定 (例如，*火球术* 需要敏捷豁免，*魅惑人类* 需要感知豁免)。
*   **熟练加值**: 如果角色熟练该特定豁免检定 (例如，“感知豁免熟练”应为 `PlayerState.proficiencies` 的一部分)，则应用此加值。
*   **豁免 DC 计算 (由具有属性的角色/NPC引起的效应)**:
    *   法术: `8 + 施法属性调整值 + 熟练加值`。
    *   其他能力: 根据能力描述指定。
*   **豁免效果**:
    *   成功: 效应通常被无效化或减弱 (例如，根据“豁免检定与伤害.htm”，伤害减半)。
    *   失败: 完整效应生效。
*   **所需数据 (`PlayerState`)**: `attributes`, `proficiencies` (获取豁免熟练), `level`。
*   **AI 交互与客户端逻辑**:
    *   **玩家角色进行豁免 (因敌人/环境效应)**:
        1.  AI 描述效应并说明所需的豁免类型 (例如，“敏捷豁免”) 和 DC (例如，“DC 13”)。
        2.  客户端为玩家进行 d20 掷骰，加上相关的属性调整值和熟练加值 (如果熟练该豁免)。
        3.  客户端告知 AI 豁免总值以及是否成功。
        4.  AI 根据豁免结果描述后果 (例如，受到全额或一半伤害，或避免了某种效应)。
    *   **敌人进行豁免 (因玩家的法术/能力)**:
        1.  玩家使用一个迫使敌人进行豁免的能力。客户端计算豁免 DC。
        2.  客户端告知 AI：“你施放了火球术。地精需要进行一次 DC 14 的敏捷豁免检定。”
        3.  AI 判定地精是否成功 (AI 内部掷骰或根据其属性/叙事需求宣告)。
        4.  AI 告知客户端：“地精豁免失败！”或“地精灵巧地躲开了部分爆炸，豁免成功。”
        5.  客户端随后进行伤害计算 (全额或一半)。

### 2.3. 伤害掷骰

*   **计算**: 掷特定的伤害骰 + 属性调整值 (对于大多数武器攻击，如果适用) + 其他加值。
*   **属性调整值 (武器伤害)**:
    *   近战武器: 力量 (默认)，或敏捷（如果攻击检定使用了灵巧属性）。
    *   远程武器: 敏捷 (通常)。
    *   投掷武器 (具有投掷属性的近战武器): 使用与该武器近战攻击相同的调整值 (力量，除非灵巧允许使用敏捷)。
    *   法术: 伤害根据法术描述。某些法术会将施法属性调整值加入伤害，另一些则不加。这需要成为法术数据的一部分。
*   **重击 (攻击检定天然 20)**:
    *   将攻击的所有伤害骰掷两次，然后加上任何相关的调整值一次。(例如，如果正常伤害是 1d8+3，重击则造成 2d8+3 伤害)。
*   **伤害类型**: 追踪伤害类型 (例如，挥砍、穿刺、钝击、火焰、寒冷等)，这对于抗性、易伤和免疫至关重要。
*   **伤害减半**: 对于某些效应豁免成功的情况。伤害减半 (向下取整)。
*   **最小伤害**: 伤害不能为负。如果调整值将伤害减至 1 以下，则造成的伤害为 0 (除非有特性指明最小为 1)。
*   **多目标 (对于迫使豁免的范围效应)**: 根据“豁免检定与伤害.htm”，对受同一效应影响的所有目标，伤害只需掷骰一次。
*   **所需数据 (`PlayerState` / 武器/法术数据)**: 伤害骰 (例如，“1d8”)，伤害类型 (例如，“挥砍”)，相关属性，法术描述 (获取调整值规则)。
*   **AI 交互与客户端逻辑**:
    *   **玩家造成伤害**:
        1.  成功命中 (或敌人豁免失败) 后，客户端计算伤害 (掷骰，加调整值，重击时双倍骰子)。
        2.  客户端告知 AI 对特定目标造成的伤害量和类型。
        3.  AI 更新其内部目标 HP 状态并描述伤害效果。
    *   **敌人造成伤害**:
        1.  敌人成功命中 (或玩家对敌人效应豁免失败) 后，AI 宣告要掷的伤害骰和任何调整值，或宣告总伤害量和伤害类型。(例如：“地精的弯刀造成 1d6+1 挥砍伤害。它掷出了 4 点，所以你受到 5 点挥砍伤害。”)
        2.  客户端将伤害应用于玩家 HP 并更新 UI。

#### 2.3.1. 武器模板与魔法武器伤害增强

为了实现更健壮和灵活的伤害计算逻辑，我们需要考虑以下方面：

*   **武器模板化**:
    *   **目的**: 为标准武器类型（如长剑、匕首、巨斧等）定义基础伤害骰、伤害类型和固有属性。这将作为计算的基础。
    *   **数据结构**: 可以在客户端预定义一个武器模板库 (例如，一个 JSON 对象或 Map)，键为武器名称或ID，值为包含其基础数据的对象。
        *   示例模板:
            ```json
            {
              "长剑": { "damage": "1d8", "damageType": "挥砍", "properties": ["多用 (1d10)"] },
              "匕首": { "damage": "1d4", "damageType": "穿刺", "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"] },
              "巨斧": { "damage": "1d12", "damageType": "挥砍", "properties": ["重型", "双手"] }
            }
            ```
    *   **玩家武器数据**: `PlayerState.equipment` 中的武器条目可以引用这些模板，并存储任何特定于该实例的修改（例如，魔法附魔）。

*   **魔法武器特性**:
    *   **固定属性加值 (例如，+1 武器)**:
        *   这类武器通常提供对攻击检定和伤害掷骰的固定加值 (例如，+1、+2、+3)。
        *   **数据表示**: 可以在武器实例数据中增加一个 `bonus: { attack: number, damage: number }` 字段。
        *   **计算逻辑**: 在计算攻击检定时加入 `bonus.attack`，在计算伤害时加入 `bonus.damage` (此加值通常在掷骰之后，属性调整值之前或之后加入，需参考具体规则书，通常是最后加入总伤害)。
    *   **附加固定伤害 (例如，+1d6 火焰伤害)**:
        *   某些魔法武器会在基础伤害之外，额外造成特定类型的固定伤害。
        *   **数据表示**: 可以在武器实例数据中增加一个 `additionalDamage: Array<{ dice: string, type: string, notes?: string }>` 字段。
            *   示例: `additionalDamage: [{ "dice": "1d6", "type": "火焰" }, { "dice": "1d4", "type": "光耀", "notes": "仅对不死生物生效" }]`
        *   **计算逻辑**: 在计算完基础武器伤害（包括属性调整值和重击效果）后，再单独掷骰计算这些附加伤害，并将它们与基础伤害合并（注意伤害类型的区分）。这些附加伤害通常不受武器重击时的双倍骰子规则影响，除非其描述明确指出。
    *   **附加法术效应**:
        *   某些武器可能具有施放特定法术的能力（例如，每日一次施放*火焰箭*），或者其攻击可能附带类似法术的效应（例如，击中时目标需进行体质豁免否则中毒）。
        *   **数据表示**:
            *   对于可施放法术的武器: 可能需要在武器数据中记录可施放的法术名称、次数、豁免DC（如果由武器决定）等。`{ castableSpells: [{ name: "火焰箭", uses: 1, per: "每日", dc?: 13 }] }`
            *   对于攻击附加效应: 可能需要在 `additionalDamage` 或一个新的 `onHitEffects` 字段中描述。`{ onHitEffects: [{ type: "saveRequired", attribute: "体质", dc: 12, effectOnFail: "中毒1分钟", notes: "击中时触发" }] }`
        *   **计算/触发逻辑**:
            *   施放法术：玩家选择使用武器施法时，触发正常的法术施放流程。
            *   攻击附加效应：在攻击命中后，客户端需要检查 `onHitEffects`，并提示AI或玩家进行相应的豁免检定或应用效果。
    *   **其他特殊魔法属性**:
        *   例如，针对特定生物类型造成额外伤害、吸血效果、诅咒效果等。这些都需要在武器数据中详细定义，并在伤害计算或命中后的逻辑中处理。

*   **伤害计算流程增强**:
    1.  **确定基础伤害**: 从武器模板获取基础伤害骰和类型。
    2.  **应用武器属性**: 考虑“多用”等属性对基础伤害骰的影响。
    3.  **掷基础伤害骰**: 掷骰。
    4.  **处理重击**: 如果是重击，将所有基础伤害骰再掷一次。
    5.  **应用属性调整值**: 将相关的属性调整值加到掷骰结果上。
    6.  **应用固定伤害加值**: 如果武器有如 `+1` 的伤害加值，则加上。
    7.  **计算附加元素/魔法伤害**: 逐个处理 `additionalDamage` 列表中的每一项：
        *   掷相应的附加伤害骰。
        *   记录其伤害类型。
        *   这些附加伤害通常不因重击而双倍骰子，除非特别说明。
    8.  **汇总总伤害**: 将所有来源的伤害（区分类型）汇总。
    9.  **应用抗性/易伤/免疫**: 在目标处根据伤害类型应用这些修正。

*   **AI 交互**:
    *   当玩家获得魔法武器时，AI应通过 `variableUpdates` 指令提供该武器的详细数据，包括其魔法属性。
    *   在战斗中，如果魔法武器的特殊效果被触发（例如，附加法术效应需要豁免），客户端应将此情况通知AI，AI负责描述后续效果和剧情发展。

通过这种方式，我们可以建立一个更细致和规则一致的伤害计算系统，能够更好地反映不同武器和魔法物品的独特性。

#### 2.3.2. 攻击性法术伤害计算

对于造成直接伤害的攻击性法术（例如，火球术、魔法飞弹等），其伤害计算需要独立于武器伤害进行处理，并考虑以下方面：

*   **法术伤害数据源**:
    *   **法术列表/数据库**: 客户端应维护一个法术列表或数据库，其中包含每个法术的详细信息，包括其伤害骰、伤害类型、是否允许豁免、豁免成功的效果（如伤害减半）、施法属性调整值是否应用于伤害等。
        *   示例数据结构 (在 `PlayerState.equippedSpells` 或一个独立的法术库中):
            ```json
            {
              "火球术": {
                "level": 3,
                "damage": "8d6",
                "damageType": "火焰",
                "save": { "attribute": "敏捷", "effectOnSuccess": "伤害减半" },
                "description": "在指定点爆发一个火球，对20尺半径球形范围内的每个生物造成8d6火焰伤害。豁免成功则伤害减半。"
              },
              "魔法飞弹": {
                "level": 1,
                "damage": "1d4+1", // 每个飞弹
                "damageType": "力场",
                "numProjectiles": 3, // 基础数量
                "higherLevelCast": "每比1环高的法术位增加一个飞弹",
                "autoHit": true, // 通常自动命中，无需攻击检定
                "description": "你创造出三枚魔法飞弹，每枚飞弹对目标造成1d4+1力场伤害。"
              },
              "火焰箭": { // 戏法示例
                "level": 0,
                "damage": "1d10", // 随角色等级提升
                "damageType": "火焰",
                "attackType": "法术攻击 (远程)",
                "scaling": { "5": "2d10", "11": "3d10", "17": "4d10" }, // 角色等级对应的伤害
                "description": "你发出一道火焰射线攻击目标，命中造成1d10火焰伤害。伤害随等级提升。"
              }
            }
            ```
    *   **通过法术名称近似检索**:
        *   当玩家选择施放一个法术时，客户端首先尝试从其已装备/已知的法术列表 (`PlayerState.equippedSpells`) 中精确查找该法术。
        *   如果AI在叙述中提到了一个玩家角色不具备的法术（例如，敌人施法），或者玩家尝试施放一个未明确记录的法术，客户端可以尝试根据法术名称从一个更广泛的预定义法术库中进行近似检索。
        *   **核心**: 客户端需要能够根据法术名称（例如，AI在选项中提供的法术名称，或玩家输入的法术名称）查询到其核心伤害参数（伤害骰、类型、豁免信息）。

*   **伤害计算逻辑**:
    1.  **确定法术和目标**: 客户端识别玩家选择施放的法术和指定的目标。
    2.  **攻击检定 (如果需要)**:
        *   对于需要法术攻击检定的法术（如“火焰箭”），按照“2.1. 攻击检定”中的法术攻击逻辑进行。
        *   如果命中，则继续进行伤害计算。
        *   如果法术自动命中（如“魔法飞弹”），则跳过攻击检定。
    3.  **确定基础伤害骰和类型**: 从法术数据中获取伤害骰 (例如 "8d6") 和伤害类型 (例如 "火焰")。
    4.  **处理等级提升效果 (戏法)**: 对于某些戏法（如“火焰箭”），伤害会随角色总等级提升。客户端需要检查角色等级并应用正确的伤害骰。
    5.  **处理高环施法 (对于有等级的法术)**: 如果法术使用高于其基础环数的法术位施放，通常会增强效果（例如，“火球术”用4环法术位施放时伤害可能增加为9d6，或“魔法飞弹”增加飞弹数量）。法术数据中应包含高环施法的规则，客户端据此调整伤害骰或效果。
    6.  **掷伤害骰**: 根据确定的伤害骰进行掷骰。
    7.  **应用施法属性调整值 (如果适用)**: 某些法术（如“魔法飞弹”的每枚飞弹，或某些牧师法术）允许将施法属性调整值加到伤害上。法术数据中应明确这一点。
    8.  **处理豁免检定**:
        *   如果法术允许豁免检定 (例如，“火球术”允许敏捷豁免)，客户端首先计算豁免DC (通常是 `8 + 施法属性调整值 + 熟练加值`)。
        *   **玩家施法**: 客户端将DC和豁免类型告知AI，AI判定目标（敌人）是否豁免成功。
        *   **敌人施法**: AI宣告法术、DC和豁免类型，客户端为玩家进行豁免检定。
        *   根据豁免结果调整伤害：
            *   豁免失败：通常造成全额伤害。
            *   豁免成功：通常造成一半伤害（向下取整），或无伤害，具体取决于法术描述。
    9.  **计算最终伤害**: 汇总所有伤害（考虑豁免结果）。
    10. **多发/多目标法术**:
        *   对于像“魔法飞弹”这样产生多个独立伤害源的法术，需要为每个伤害源单独计算伤害（但通常共享一次施法属性调整值，如果适用）。
        *   对于范围法术（如“火球术”），伤害掷骰只进行一次，该结果应用于范围内的所有目标（每个目标独立进行豁免）。
    11. **应用抗性/易伤/免疫**: 在目标处根据伤害类型应用这些修正。

*   **AI 交互**:
    *   **玩家施法**:
        *   客户端: "玩家施放[法术名称]攻击[目标]。[如果是攻击检定法术，则附加攻击检定结果]。[如果法术需要豁免，则告知AI：目标需进行DC[数值]的[属性]豁免]。"
        *   AI (如果需要敌人豁免): "[敌人名称]豁免[成功/失败]！"
        *   客户端 (计算伤害后): "对[目标]造成了[数值][类型]伤害。"
        *   AI: 描述伤害效果和后续剧情。
    *   **敌人施法**:
        *   AI: "[敌人名称]施放[法术名称]攻击你！[如果是攻击检定法术，则宣告攻击检定结果]。[如果法术需要豁免，则宣告：你需要进行DC[数值]的[属性]豁免检定！]"
        *   客户端 (如果需要玩家豁免): "玩家豁免[成功/失败]！豁免掷骰结果为[总值]。"
        *   AI: "你[豁免成功/失败]，受到了[数值][类型]伤害！[描述效果]"
        *   客户端: 更新玩家HP。

*   **数据结构考量 (`PlayerState.equippedSpells` 或法术库)**:
    *   `damage: string` (例如, "8d6", "1d4+MOD")
    *   `damageType: string`
    *   `attackType?: "法术攻击 (近战)" | "法术攻击 (远程)" | "自动命中"`
    *   `save?: { attribute: string, effectOnSuccess: "伤害减半" | "无效果" | string }`
    *   `addCastingModifierToDamage?: boolean`
    *   `numProjectiles?: number` (用于如魔法飞弹)
    *   `scaling?: { [level: number]: string }` (用于戏法伤害随角色等级提升)
    *   `higherLevelCast?: { perSlotAboveBase: string, effect: string }` (例如, "每高于基础环阶的法术位"，"增加1d6伤害" 或 "增加一个飞弹")

通过细化法术伤害的计算逻辑和数据表示，可以更准确地模拟D&D 5e中各种法术的效果。

#### 2.3.3. 复杂伤害叠加、状态效果与动作管理 (客户端核心逻辑)

为了准确处理如“至圣斩”这类可叠加多种伤害来源的技能，以及管理各种状态效果和玩家的动作（包括附赠动作），客户端需要承担主要的规则驱动计算和状态追踪任务。AI则更侧重于叙事和提供触发条件。

*   **增强的 `PlayerState` 和 `EnemyStateJSON` 结构**:
    *   **`PlayerState`**:
        *   `activeEffects: Array<{ name: string, source: string, duration?: string | number, description?: string, modifiers?: object, effectId: string, targetId?: string }>`: 用于追踪玩家当前生效或施加给其他目标的增益（Buffs）、减益（Debuffs）及其他持续性效果。
            *   `name`: 效果名称 (例如："祝福术", "妖火目标", "至圣斩就绪", "强令对决目标")。
            *   `source`: 效果来源 (例如："法术：祝福术", "职业特性：神恩", "物品：某某护符", "玩家施放：强令对决")。
            *   `duration`: 持续时间 (例如："1分钟", "直到下次长休", 或具体的轮数)。客户端需要逻辑来追踪和移除过期的效果。
            *   `description`: 效果的简要描述。
            *   `modifiers`: 一个对象，包含该效果提供的具体数值或状态修饰。例如：`{ attackBonus: "1d4", damageBonus: { dice: "1d4", type: "光耀" }, savingThrowBonus: "1d4", advantageOnAttacksAgainstTarget: true, targetHasDisadvantageOnSave: "魅力", movementRestriction: "mustMoveTowardsSource" }`。
            *   `effectId`: 效果的唯一标识符，便于追踪和移除。
            *   `targetId`: (可选) 如果此效果是施加给特定目标（例如敌人），则记录目标ID。
        *   `classFeatures: Array<{ name: string, uses?: { current: number, max: number, per: string }, description: string, relatedSpells?: string[], actionType?: "动作" | "附赠动作" | "反应" | "被动" | "可选增强", trigger?: "攻击命中时" | "施法时" | string, cost?: string }>`: 记录职业特性。
            *   例如“至圣斩”：`{ name: "至圣斩", description: "消耗法术位对武器攻击附加光耀伤害...", actionType: "可选增强", trigger: "武器攻击命中时", cost: "法术位" }`
        *   `spellSlots`: (已存在) 需确保能准确追踪各环阶法术位的消耗。
        *   `usedBonusActionThisTurn: boolean`: 标记本回合是否已使用附赠动作。
        *   `usedReactionThisTurn: boolean`: 标记本回合是否已使用反应。
    *   **`EnemyStateJSON`**:
        *   类似地，也应有 `activeEffects: Array<{ name: string, source: string, duration?: string | number, description?: string, modifiers?: object, effectId: string, sourcePlayerId?: string }>` 字段来追踪敌人身上的持续效果，并记录效果来源（例如，哪个玩家施加的）。

*   **客户端伤害计算与决策流程 (以“至圣斩”为例)**:
    1.  **攻击命中**: 玩家的武器攻击检定成功。
    2.  **客户端检查可选增强**: 客户端遍历 `PlayerState.classFeatures` 和 `PlayerState.activeEffects`，查找是否有适用于当前攻击（例如，`trigger: "武器攻击命中时"`）的可选伤害增强特性或效果，如“至圣斩”或已激活的“圣化武器”法术效果。
    3.  **玩家决策提示 (如果适用)**:
        *   如果“至圣斩”可用（有法术位，且是圣武士职业），客户端在UI上提示玩家：“你的攻击命中了！是否使用‘至圣斩’？”并可能提供不同环阶法术位的选项（基于可用法术位）。
        *   这可以通过在 `actionChoicesArea` 临时增加按钮，或一个简单的模态对话框实现。
    4.  **收集所有伤害来源**:
        *   **基础武器伤害**: 掷骰，应用属性调整值，处理重击（双倍伤害骰）。
        *   **魔法武器固定加值**: 应用（例如，+1 伤害）。
        *   **魔法武器附加伤害骰**: 掷骰（例如，1d6 火焰）。
        *   **至圣斩伤害**: 如果玩家选择使用，并选择了法术位环阶，则根据规则（例如，1环2d8光耀，每高一环+1d8，对不死/邪魔额外+1d8）计算并掷光耀伤害骰。客户端消耗相应法术位。
        *   **其他职业特性伤害**: 如果有其他被动或已触发的职业特性增加伤害，则计算并掷骰。
        *   **持续性法术Buff伤害**: 检查 `PlayerState.activeEffects` 中是否有增加本次攻击伤害的Buff（例如，“圣化武器”的1d4光耀），计算并掷骰。
    5.  **汇总伤害**: 将所有来源的伤害按类型（挥砍、穿刺、火焰、光耀等）分别汇总。
    6.  **通知AI**: 将最终计算出的、按类型分类的总伤害量告知AI。例如：“玩家对地精造成了 8点挥砍伤害 和 9点光耀伤害（来自至圣斩）。”

*   **状态效果管理 (例如，“强令对决”)**:
    *   **施加效果**: 当玩家成功施放如“强令对决”的法术，且目标豁免失败时：
        1.  AI描述目标受到影响的叙事。
        2.  AI通过 `variableUpdates` 指令，在目标敌人 (`EnemyStateJSON.activeEffects`) 或玩家 (`PlayerState.activeEffects`) 的状态中添加一个代表该效果的条目，包含效果名称、来源、持续时间、具体修饰（如对非施法者攻击劣势，必须向施法者移动等）。
    *   **客户端应用效果**:
        *   **攻击检定**: 当受“强令对决”影响的生物攻击非施法者时，客户端在执行其攻击检定时自动应用劣势。
        *   **移动**: AI在描述受影响生物的移动时，应考虑到效果的限制（例如，“地精在‘强令对决’的影响下，不情愿地向你移动…”）。客户端本身不强制移动，但AI的叙事和提供的行动选项应反映此限制。
        *   **豁免检定**: 如果效果影响豁免（例如，提供优势或劣势），客户端在进行豁免检定时应用。
    *   **效果持续与移除**: 客户端需要追踪效果的持续时间（例如，轮数）。当持续时间结束，或特定条件满足时（例如，施法者失去专注），客户端自动从 `activeEffects` 列表中移除该效果，并可能通知AI（例如，“对[敌人名称]的强令对决效果已结束”），AI据此调整叙事。

*   **附赠动作与反应管理**:
    *   **追踪**: 客户端在每回合开始时重置 `PlayerState.usedBonusActionThisTurn` 和 `PlayerState.usedReactionThisTurn` 为 `false`。
    *   **提供选项**:
        *   当玩家执行了主动作（如“攻击”动作）后，如果其职业特性、法术或装备允许通过附赠动作执行额外行动（例如，双武器战斗的副手攻击、某些法术的附赠动作施法部分），客户端检查 `usedBonusActionThisTurn` 是否为 `false`。如果是，则可以在 `playerChoices` 中动态生成代表该附赠动作的选项。
        *   反应类动作（如借机攻击、施放护盾术）通常在特定时机触发（敌人移动离开触及、被攻击命中）。AI的叙述会提供这些触发时机，客户端随后可以向玩家提供是否使用反应的选项。
    *   **消耗**: 玩家选择并执行了附赠动作或反应后，客户端将对应的标记（`usedBonusActionThisTurn` 或 `usedReactionThisTurn`）设为 `true`，本回合不能再使用同类动作。
    *   **AI交互**: AI不需要直接管理玩家的动作经济，但其提供的场景和敌人行动会创造使用这些动作的机会。客户端负责管理动作的可用性并据此过滤或提供选项。

*   **AI的角色**:
    *   AI不直接计算复杂伤害总和或管理玩家的动作经济。
    *   AI根据客户端告知的计算结果（伤害、检定成功/失败）和玩家选择的动作，进行叙事描述，推动剧情发展，并控制NPC的行为（应考虑到NPC自身的状态效果）。
    *   AI通过 `variableUpdates` 指令来施加或移除由其控制的效应（例如，NPC施放的法术效果）。

通过这种职责划分，客户端保证了规则执行的精确性，而AI则专注于创造生动有趣的故事情节和互动。

### 2.4. 武器属性实施

*   **表示**: `PlayerState.equipment[]` 项目应具有 `properties: string[]` 字段 (例如，`["灵巧", "轻型", "投掷 (射程 20/60)"]`)。武器数据 (伤害、属性) 应可访问，可能来自预定义列表或嵌入在 `PlayerState` 中。
*   **需实施的“武器.htm”中的关键属性**:
    *   **弹药 (例如，`弹药（射程 80/320；弩矢）`)**: 需要弹药。客户端需要追踪玩家是否有相应弹药。如果没有，则无法攻击。
    *   **灵巧 (`Finesse`)**: 允许使用力量或敏捷进行攻击和伤害掷骰 (取调整值较高者)。
    *   **重型 (`Heavy`)**: (初期可选) 小型生物使用重型武器可能有劣势。
    *   **轻型 (`Light`)**: 与双武器战斗相关。
    *   **装填 (`Loading`)**: 限制使用动作、附赠动作或反应攻击时武器的射击次数 (通常为一次，无论通常允许多少次攻击)。
    *   **射程 (`Range X/Y`)**: 定义远程武器的正常射程 (X) 和远射程 (Y)。远射程 (Y) 攻击有劣势。超出远射程 (Y) 的攻击自动失手。
    *   **触及 (`Reach`)**: 扩展近战攻击范围 (通常对如长柄刀、戟、鞭等武器扩展至 10 尺)。
    *   **投掷 (`Thrown (射程 X/Y)`)**: 允许使用近战武器进行远程攻击。攻击和伤害使用与该武器近战攻击相同的属性调整值 (力量，除非灵巧允许使用敏捷)。
    *   **双手 (`Two-Handed`)**: 需要双手使用。无法同时使用盾牌。
    *   **多用 (`Versatile (XdY)`)**: 可单手使用 (主要伤害骰) 或双手使用 (更大的 XdY 伤害骰)。如果副手没有使用盾牌或其他物品，则由玩家选择。
*   **精通属性 (来自玩家手册 2024 “武器.htm”)**: `缓速 (Slow)`, `迅击 (Nick)`, `推离 (Push)`, `侵扰 (Vex)`, `削弱 (Sap)`, `失衡 (Topple)`, `擦掠 (Graze)`, `横扫 (Cleave)`。
    *   这些会显著增加复杂性。**建议**: 初期 v3 暂缓完整实施。承认其存在，但首先关注核心属性。
*   **AI 交互**: AI 在描述敌人行动或玩家使用具有显著属性的武器时，应被提示注意武器属性。客户端处理这些属性的机制效果。

### 2.5. 近战攻击细节

*   **标准触及**: 5 尺，除非武器具有触及属性。
*   **借机攻击**:
    *   **触发**: 玩家可见的一个敌对生物移出玩家的触及范围。
    *   **动作消耗**: 消耗玩家每轮一次的反应。
    *   **攻击**: 一次近战攻击 (武器或徒手)。
    *   **时机**: 发生在生物离开玩家触及范围之前的一刻。
    *   **避免**: 撤离动作、传送，或不消耗动作、附赠动作或移动的移动 (例如，被强制移动)。
    *   **客户端逻辑**: 如果没有详细的网格/位置系统，这部分完全实现会很复杂。
        *   **简化方法**: AI 明确说明敌人何时引发了玩家的借机攻击 (例如，“地精转身逃跑，引发了你的借机攻击！你要进行攻击吗？”)。然后由玩家选择。
    *   **AI 端逻辑**: AI 控制的敌人如果玩家角色移出其触及范围，也应能进行借机攻击。AI 会宣告此事。

### 2.6. 远程攻击细节

*   **射程增量**: 正常射程 (X) 和远射程 (Y)。
    *   正常射程内的攻击正常进行。
    *   超出正常射程但在远射程内 (至多 Y) 的攻击，攻击检定具有劣势。
    *   超出远射程 (Y) 的攻击自动失手。
*   **近战中攻击 (远程攻击具有劣势)**: 如果玩家在距离一个敌对生物 5 尺内进行远程攻击，则攻击检定具有劣势。

### 2.7. 其他需考虑的战斗规则

*   **双武器战斗 (基础)**:
    *   当执行攻击动作并用一只手持用的轻型近战武器攻击时，玩家可以用一个附赠动作使用另一只手持用的不同轻型近战武器进行攻击。
    *   玩家不将属性调整值加到附赠攻击的伤害中，除非该调整值为负。
    *   **客户端逻辑**: 如果玩家装备了两把轻型武器，并执行了攻击动作，可以提供一个附赠攻击的选项，或者如果 AI 知晓此情况则自动处理。
*   **掩蔽 (简化)**:
    *   AI 可以叙述性地描述掩蔽。
    *   **简化机制**: AI 可能说明“地精具有半身掩蔽 (+2 AC)”或“四分之三掩蔽 (+5 AC)”。客户端在玩家攻击时将此应用于目标 AC。玩家也可能从掩蔽中受益。
*   **优势和劣势**:
    *   掷两次 d20，优势取高值，劣势取低值。
    *   如果多种情况同时提供优势和/或劣势，若两种类型都存在则相互抵消；否则，只获得一次优势/劣势。
    *   **客户端逻辑**: `performCheck` 函数需要接受一个优势/劣势状态参数。
    *   **AI 交互**: AI 说明何时某种情况会提供优势或劣势 (例如，“敌人处于倒地状态，你对其进行的近战攻击具有优势。”)。

## 3. 数据结构更新

*   **`PlayerState.equipment[]` / `PlayerState.inventory[]` (武器)**:
    *   确保包含字段: `name: string` (名称), `damage: string` (伤害骰，例如 "1d8"), `damageType: string` (伤害类型，例如 "挥砍"), `properties: string[]` (属性，例如 `["灵巧", "轻型", "投掷 (射程 20/60)", "多用 (1d10)"]`)。
*   **`PlayerState.proficiencies[]`**:
    *   需要清晰列出特定的武器熟练项 (例如，“长剑”，“短弓”)，护甲类型，以及豁免属性 (例如，“力量豁免”，“敏捷豁免”)。
*   **`EnemyStateJSON`**:
    *   `ac: number` (已存在)。
    *   `attacks: EnemyAttack[]` (新增或增强):
        *   `EnemyAttack`: `{ name: string, toHitBonus?: number, attackRollFormula?: string, damageFormula: string, damageType: string, properties?: string[], reach?: number, saveDC?: number, saveAttribute?: string, effectDescription?: string }`。AI 可能提供 `toHitBonus`，或者如果敌人属性详细，则期望客户端根据属性计算。
    *   `vulnerabilities: string[]` (易伤), `resistances: string[]` (抗性), `immunities: string[]` (免疫) (针对伤害类型)。
*   **`AdventureSceneJSON.playerChoices[].checkDetails` (或类似机制)**:
    *   对于玩家攻击，选项文本应清晰表明意图 (例如，“用长剑攻击地精”)。
    *   客户端需要从 `PlayerState.equipment` 中查找长剑的详细信息。
*   **AI 提示 (`adventure_log_v3_ai_prompts.md`)**:
    *   更新 AI 提示，以反映客户端将处理玩家端的掷骰。
    *   指导 AI 如何宣告敌人攻击 (完整掷骰结果或供客户端掷骰的参数)。
    *   指导 AI 如何宣告需要玩家进行豁免检定的效应 (DC、属性、成功/失败效果)。
    *   指导 AI 在叙述上重要时描述敌人对武器属性的运用。

## 4. 客户端逻辑 (`src/adventure_log_v3/index.ts`)

*   **`performCheck` 函数 (扩展)**:
    *   接受参数 `checkType: "attack" | "save" | "ability"`。
    *   接受参数 `advantageState: "advantage" | "disadvantage" | "normal"`。
    *   对于攻击：整合灵巧逻辑，处理天然 1/天然 20。
    *   对于豁免：使用正确的属性和熟练。
*   **新函数 `calculateDamageRoll(damageFormula: string, abilityModifier: number, isCritical: boolean, weaponProperties?: string[])`**:
    *   解析 `damageFormula` (例如, "1d8", "2d6+MOD")。
    *   掷骰。
    *   如果公式中包含 "+MOD" 或武器类型暗示，则加上 `abilityModifier`。
    *   对于 `isCritical`，伤害骰双倍 (调整值只加一次)。
    *   返回总伤害。
*   **`handleActionChoice` 函数**:
    *   **玩家攻击**:
        1.  解析选项以识别目标和武器/法术。
        2.  从 `PlayerState` 检索武器/法术属性 (伤害、特性、攻击属性)。
        3.  调用 `performCheck` (类型 "attack") 获取攻击检定结果。
        4.  如果命中，调用 `calculateDamageRoll` 获取伤害。
        5.  构建给 AI 的详细反馈: `玩家用[武器/法术]攻击[目标]。[攻击检定: d20[掷骰结果]+调整值[数值]+熟练[数值]=总值 vs AC[AC值] -> 命中/失手/重击！]。[如果命中，伤害: 掷骰[骰子结果]+调整值[数值]=总伤害[类型]伤害。]`
    *   **玩家法术 (迫使豁免)**:
        1.  解析选项以识别法术和目标。
        2.  计算豁免 DC (`8 + 施法调整值 + 熟练加值`)。
        3.  告知 AI: `玩家对[目标]施放[法术]。他们必须进行一次 DC[DC值]的[属性]豁免检定。`
    *   **玩家属性检定**: (现有逻辑，确保使用扩展后的 `performCheck`)。
*   **处理 AI 响应 (敌人回合 / 效应结算)**:
    *   **敌人攻击宣告**:
        *   如果 AI 宣告完整掷骰 (例如，“地精攻击，掷出 18 点命中，造成 5 点穿刺伤害”): 如果玩家 AC 被满足，客户端应用伤害。
        *   如果 AI 宣告攻击参数 (例如，“地精用弯刀攻击 (+4 命中，1d6+2 挥砍伤害)”): 客户端为敌人掷 d20+4，与玩家 AC 比较，如果命中，客户端掷 1d6+2 伤害。(这对 AI 来说更复杂)。
    *   **玩家被强制豁免的结算**:
        *   AI 根据玩家报告的豁免结果说明后果 (例如，“你豁免成功，只受到一半伤害：3 点火焰伤害。”或“你豁免失败，受到 6 点火焰伤害。”)。
        *   客户端根据 `variableUpdates` 或叙述应用伤害/效应。
*   **UI 更新**:
    *   显示玩家行动的详细掷骰分解 (d20 + 调整值 = 总值 vs DC/AC)。
    *   清晰显示造成/受到的伤害和 HP 变化。

## 5. 分阶段实施方法

1.  **阶段一: 玩家攻击检定与伤害**:
    *   实施玩家攻击检定 (近战和远程)，包含正确的属性调整值 (包括灵巧)、熟练、天然1/20。
    *   实施武器伤害计算 (属性调整值、重击)。
    *   客户端向 AI 发送详细的攻击检定和伤害信息。AI 描述结果。
    *   对于敌人攻击：AI 宣告其攻击检定总值和伤害总值。客户端对照玩家 AC 并应用伤害。
    *   更新 `PlayerState` 以包含武器数据 (伤害骰、类型、灵巧、投掷、射程、多用)。
2.  **阶段二: 玩家豁免检定与基础法术效应**:
    *   实施玩家针对 AI 指定的 DC 和属性进行的豁免检定。
    *   客户端向 AI 发送豁免结果。AI 描述后果 (例如，伤害减半，施加状态)。
    *   实施玩家施放迫使敌人豁免的法术的逻辑 (客户端计算 DC，AI 判定敌人豁免结果)。
    *   更新 `PlayerState` 以包含豁免熟练和基础法术数据。
3.  **阶段三: 高级战斗规则与 AI 敌人行动**:
    *   实施远程攻击劣势 (远射程、目标在近战范围内)。
    *   实施借机攻击 (简化：AI 提示玩家)。
    *   实施双武器战斗 (附赠动作攻击)。
    *   优化 AI 敌人攻击宣告：AI 为其攻击/法术提供更详细的参数，如果为了保持一致性，可能允许客户端为某些敌人行动掷骰，或者 AI 继续宣告完整结果。
    *   基础掩蔽机制 (AI 宣告 AC 加值)。
4.  **阶段四: 状态效果、抗性/易伤、精通属性 (未来)**:
    *   实施常见状态效果 (例如，倒地、束缚) 及其机制效果。
    *   为玩家和敌人实施伤害抗性、易伤、免疫。
    *   考虑武器精通属性。

## 6. 文档更新

*   更新 `src/adventure_log_v3/adventure_log_v3_ai_prompts.md` 以反映新的战斗 AI 交互模式，详细说明客户端的预期输入和 AI 的预期输出。
*   更新 `src/adventure_log_v3/README.md` 以包含新的战斗特性。

此计划为彻底修改战斗系统提供了一个结构化的方法。每个阶段都将建立在前一个阶段的基础上，允许迭代开发和测试。
