import { calculateDamageRoll, performCheck } from './combat'; // Corrected path
import {
  applyVariableUpdate,
  currentSceneData,
  equipItem,
  generateInitialAdventureSceneJSON,
  getDefaultPlayerState,
  loadCharacterDataFromLorebook,
  loadGameState,
  loadWeaponTemplates,
  parseAIResponse,
  persistGameState,
  playerState,
  setCurrentSceneData,
  setPlayerState, // Import new functions
  unequipItem,
  weaponTemplates,
} from './core/state'; // Corrected path
import {
  loadSpellTemplates,
  reloadSpellTemplates,
  getSpellTemplate,
  calculateSpellDamage,
  canCastSpell,
  consumeSpellSlot,
  performSpellAttack,
} from './spells'; // Import spell system functions
import { AdventureSceneJSON, MagicEffect, PlayerChoiceJSON, PlayerState } from './types'; // Corrected path
import {
  actionChoicesArea,
  adventureLogContainer,
  backpackInterface,
  closeBackpackButton,
  detailedCharacterSheet,
  initializeDomElements,
  mainNarrativeArea,
  playerStatusArea,
  startNewGameButton,
  startScreenContainer,
  // New backpack elements
  toggleBackpackButton,
  toggleCharSheetButton,
  // New spellbook elements
  toggleSpellbookButton,
  spellbookInterface,
  closeSpellbookButton,
  spellDetailModal,
  closeSpellDetailButton,
  castSpellButton,
  cancelSpellButton,
  spellLevelFilter,
  spellSchoolFilter,
  spellTargetTypeSelect,
  spellTargetCustomInput,
  customTargetInputDiv,
} from './ui/domElements'; // Corrected path
import {
  renderActionChoices,
  // New backpack rendering functions
  renderCurrencyDisplay,
  renderEquippedItemsDisplay,
  renderInventoryDisplay,
  renderNarrative,
  updatePlayerStatusDisplay,
  // New spellbook rendering functions
  renderSpellbookInterface,
  renderAvailableSpellsList,
  showSpellDetailModal,
  updateSpellTargetOptions,
  handleTargetTypeChange,
  getSelectedTargetName,
} from './ui/render'; // Corrected path
import { ensureGlobals, getAmmunitionType, getEffectiveWeaponProperties, getProficiencyBonus, performSaveThrow, quickSaveTest, safeToastr, testCheckParsing, testSpellSaveLogic } from './utils'; // Corrected path

// SillyTavern global functions
declare function triggerSlash(command: string): Promise<string | undefined>;
declare function getLastMessageId(): number | undefined;
declare function setChatMessages(
  messages: { message_id: number; message: string }[],
  options?: { refresh?: string },
): Promise<void>;

export function applySceneData(scene: AdventureSceneJSON | null) {
  if (!scene) {
    if (mainNarrativeArea) mainNarrativeArea.innerHTML = '<p>错误：无法加载场景数据。</p>';
    if (actionChoicesArea) actionChoicesArea.innerHTML = '';
    return;
  }

  // Idempotency check
  if (scene.sceneUuid && playerState.lastProcessedSceneUuid === scene.sceneUuid) {
    console.warn(
      `[AdvLogV3] Scene ${scene.sceneUuid} already processed. Skipping variable updates. Rendering UI only.`,
    );
    setCurrentSceneData(scene); // Still update currentSceneData for UI consistency
    renderNarrative(scene);
    updatePlayerStatusDisplay(playerState); // Render with the existing playerState
    renderActionChoices(scene.playerChoices, handleActionChoice);
    // Enable buttons again if they were disabled by a previous quick succession of calls
    if (actionChoicesArea) {
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
    }
    // Remove waiting message if it exists
    const waitingMsg = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsg?.parentElement && mainNarrativeArea) mainNarrativeArea.removeChild(waitingMsg.parentElement);
    return;
  }

  setCurrentSceneData(scene);

  // Create a mutable copy for updates within this function's scope
  const newPlayerStateSnapshot = JSON.parse(JSON.stringify(playerState));
  newPlayerStateSnapshot.currentLocation = scene.currentLocation;
  newPlayerStateSnapshot.time = scene.time;

  if (scene.variableUpdates && scene.variableUpdates.length > 0) {
    scene.variableUpdates.forEach(update => {
      let actualTargetObject: any = null;

      if (update.target === '玩家') {
        actualTargetObject = newPlayerStateSnapshot;
      } else {
        // At this point, if target is not '玩家', it should be an enemy ID string.
        // We'll add a more robust check.
        const targetStr = update.target as string; // Use type assertion carefully or ensure type guard is effective
        if (typeof targetStr === 'string' && targetStr.startsWith('敌人ID:')) {
          const enemyIdToUpdate = targetStr.substring('敌人ID:'.length);
          // Ensure scene.enemies is an array before trying to find an enemy
          if (scene.enemies && Array.isArray(scene.enemies)) {
            const enemyToUpdate = scene.enemies.find(enemy => enemy.id === enemyIdToUpdate);
            if (enemyToUpdate) {
              actualTargetObject = enemyToUpdate;
            } else {
              console.warn(
                `[applySceneData] Enemy with ID ${enemyIdToUpdate} not found in current scene for variable update.`,
              );
            }
          } else {
            console.warn(
              `[applySceneData] scene.enemies is not an array or is undefined. Cannot update enemy ID: ${enemyIdToUpdate}`,
            );
          }
        } else {
          // This else belongs to the "if (typeof targetStr === 'string' && targetStr.startsWith('敌人ID:'))"
          console.warn(`[applySceneData] Target '${update.target}' is not '玩家' and not a valid enemy ID format.`);
        }
      } // This closes the "else" for "if (update.target === '玩家')"

      if (actualTargetObject) {
        applyVariableUpdate(actualTargetObject, update.path, update.operation, update.value);
      }
    });
  }

  // After all updates, set the new UUID and then update the global state
  if (scene.sceneUuid) {
    newPlayerStateSnapshot.lastProcessedSceneUuid = scene.sceneUuid;
  }
  setPlayerState(newPlayerStateSnapshot); // Atomically update the global playerState

  renderNarrative(scene);
  updatePlayerStatusDisplay(playerState); // playerState is now the newPlayerStateSnapshot
  renderActionChoices(scene.playerChoices, handleActionChoice);

  // If backpack is open, refresh its content
  if (backpackInterface && backpackInterface.style.display !== 'none') {
    renderCurrencyDisplay(playerState);
    renderEquippedItemsDisplay(playerState); // Pass playerState
    renderInventoryDisplay(playerState); // Pass playerState
  }
}

async function handleActionChoice(choice: PlayerChoiceJSON) {
  if (actionChoicesArea) {
    actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = true));
  }
  const currentMainNarrativeArea = mainNarrativeArea;
  if (currentMainNarrativeArea) {
    const waitingP = document.createElement('p');
    waitingP.innerHTML = '<i>正在等待AI响应...</i>';
    currentMainNarrativeArea.appendChild(waitingP);
  }

  let promptContext = '';
  if (currentSceneData) {
    promptContext += `紧接之前的场景JSON数据是:\n${JSON.stringify(currentSceneData, null, 2)}\n`;
  }

  let prompt = `你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志 v2”。\n`;
  prompt += `当前玩家状态：\n${JSON.stringify(playerState, null, 2)}\n`;
  prompt += `\n${promptContext}`;

  let checkFeedbackToAI = '';

  // 多重正则表达式匹配，提高检定解析的鲁棒性
  const checkRegexPatterns = [
    // 标准格式: [DC15 力量(运动)]
    /\[DC(\d+)\s+([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(?:检定)?\]/i,
    // 变体格式1: [力量(运动) DC15]
    /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:检定)?\]/i,
    // 变体格式2: [魅力(威吓) DC14 - 软硬兼施]
    /\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:-\s*[^\]]+)?\]/i,
    // 变体格式3: [DC13 魅力检定(游说)]
    /\[DC(\d+)\s+([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
    // 变体格式4: [进行DC15的力量检定]
    /\[(?:进行)?DC(\d+)(?:的)?([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,
  ];

  const attackCheckRegex = /\[攻击\s+(.+?)\s+使用\s+(.+?)\s+DC(\d+)\]/i;

  let checkMatch: RegExpMatchArray | null = null;
  let checkDC: number = 0;
  let checkAttribute: string = '';
  let checkSkill: string | undefined = undefined;

  // 尝试匹配各种检定格式
  for (let i = 0; i < checkRegexPatterns.length; i++) {
    const regex = checkRegexPatterns[i];
    const match = choice.text.match(regex);
    if (match) {
      checkMatch = match;
      // 根据不同的正则表达式格式解析参数
      if (i === 0 || i === 3 || i === 4) {
        // 标准格式: [DC15 力量(运动)] 或 [DC13 魅力检定(游说)] 或 [进行DC15的力量检定]
        checkDC = parseInt(match[1], 10);
        checkAttribute = match[2].trim();
        checkSkill = match[3] ? match[3].trim() : undefined;
      } else if (i === 1 || i === 2) {
        // 变体格式: [力量(运动) DC15] 或 [魅力(威吓) DC14 - 软硬兼施]
        checkAttribute = match[1].trim();
        checkSkill = match[2] ? match[2].trim() : undefined;
        checkDC = parseInt(match[3], 10);
      }

      // 调试信息：显示匹配到的检定格式
      const formatNames = [
        '标准格式 [DC15 力量(运动)]',
        '变体格式1 [力量(运动) DC15]',
        '变体格式2 [魅力(威吓) DC14 - 描述]',
        '变体格式3 [DC13 魅力检定(游说)]',
        '变体格式4 [进行DC15的力量检定]'
      ];
      safeToastr('info', `检定解析成功: ${formatNames[i]} -> DC${checkDC} ${checkAttribute}${checkSkill ? `(${checkSkill})` : ''}`, '检定解析');
      break;
    }
  }

  let attackMatch = choice.text.match(attackCheckRegex);

  // Handle spell casting commands
  if (choice.actionCommand?.startsWith('cast_spell_save:')) {
    const [, spellName, targetName, slotLevelStr] = choice.actionCommand.split(':');
    const slotLevel = parseInt(slotLevelStr) || 0;

    const spellTemplate = getSpellTemplate(spellName);
    if (spellTemplate && spellTemplate.save && canCastSpell(spellName, playerState)) {
      // Calculate spell save DC using correct 5e proficiency bonus
      const spellcastingMod = playerState.attributes[playerState.spellcastingAbility?.toLowerCase() as keyof typeof playerState.attributes]?.mod || 0;
      const proficiencyBonus = getProficiencyBonus(playerState.level); // Use correct 5e proficiency bonus
      const spellSaveDC = 8 + spellcastingMod + proficiencyBonus;

      // Use the new save throw calculation system
      // Estimate target level based on spell level (rough approximation)
      const estimatedTargetLevel = Math.max(1, spellTemplate.level * 2);
      const targetProficient = Math.random() < 0.3; // 30% chance target is proficient in this save

      const saveResult = performSaveThrow(
        spellSaveDC,
        spellTemplate.save.attribute,
        estimatedTargetLevel,
        targetProficient
      );

      // Consume spell slot if not a cantrip
      if (spellTemplate.level > 0) {
        consumeSpellSlot(playerState, slotLevel);
      }

      const saveResultText = saveResult.success ? '豁免成功' : '豁免失败';

      // Create detailed feedback for AI
      let detailedRollInfo = `投骰1d20[${saveResult.roll}]`;
      if (saveResult.modifier > 0) {
        detailedRollInfo += `+${saveResult.modifier}(${saveResult.attribute})`;
      } else if (saveResult.modifier < 0) {
        detailedRollInfo += `${saveResult.modifier}(${saveResult.attribute})`;
      }
      detailedRollInfo += `=${saveResult.total}`;

      // Add special indicators for critical results
      if (saveResult.isCritical) {
        detailedRollInfo += ' [天然20]';
      } else if (saveResult.isFumble) {
        detailedRollInfo += ' [天然1]';
      }

      checkFeedbackToAI = ` [${spellName} 法术 DC${spellSaveDC} ${saveResult.attribute}豁免 ${detailedRollInfo} ${saveResultText}]`;

      // Create user-friendly message
      const toastMessage = `${spellName} 对 ${targetName}: DC${spellSaveDC} ${saveResult.attribute}豁免 -> ${saveResultText} (${detailedRollInfo})`;

      // Use correct toast colors: success for player benefit (save failed), info for neutral/target benefit (save succeeded)
      safeToastr(saveResult.success ? 'info' : 'success', toastMessage, '法术豁免结果');
      if (mainNarrativeArea) {
        mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
      }
    }
  } else if (choice.actionCommand?.startsWith('cast_spell_utility:')) {
    const [, spellName, slotLevelStr] = choice.actionCommand.split(':');
    const slotLevel = parseInt(slotLevelStr) || 0;

    const spellTemplate = getSpellTemplate(spellName);
    if (spellTemplate && canCastSpell(spellName, playerState)) {
      // Consume spell slot if not a cantrip
      if (spellTemplate.level > 0) {
        consumeSpellSlot(playerState, slotLevel);
      }

      checkFeedbackToAI = ` [施放 ${spellName} 法术成功]`;
      safeToastr('success', `成功施放 ${spellName}`, '法术施放');
      if (mainNarrativeArea) {
        mainNarrativeArea.innerHTML += `<p class="check-result"><em>你施放了 ${spellName}</em></p>`;
      }
    }
  }

  if (attackMatch) {
    const targetName = attackMatch[1].trim();
    const weaponOrSpellName = attackMatch[2].trim();
    const dc = parseInt(attackMatch[3], 10);

    let attributeToUse: 'strength' | 'dexterity' | 'intelligence' | 'wisdom' | 'charisma' = 'strength';
    let isSpellAttack = false;
    const weapon = playerState.equipment?.find(e => e.name === weaponOrSpellName && e.equipped);
    const spell = playerState.equippedSpells?.find(s => s.name === weaponOrSpellName);
    let advantageState: 'advantage' | 'disadvantage' | 'normal' = 'normal';

    if (weapon && !isSpellAttack) {
      const weaponProps = getEffectiveWeaponProperties(weapon, weaponTemplates);
      const ammoType = getAmmunitionType(weaponProps);
      if (ammoType) {
        const ammoInInventory = playerState.inventory?.find(item => item.name === ammoType);
        if (!ammoInInventory || ammoInInventory.quantity <= 0) {
          safeToastr('error', `无法攻击：缺少弹药 (${ammoType})！`, '弹药不足');
          if (mainNarrativeArea) {
            const noAmmoMsg = document.createElement('p');
            noAmmoMsg.className = 'system-message';
            noAmmoMsg.innerHTML = `<em>你试图使用 ${weapon.name}，但发现没有 ${ammoType} 了！</em>`;
            mainNarrativeArea.appendChild(noAmmoMsg);
          }
          if (actionChoicesArea) {
            actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
          }
          const waitingMsg = mainNarrativeArea?.querySelector('p > i');
          if (waitingMsg?.parentElement && mainNarrativeArea) mainNarrativeArea.removeChild(waitingMsg.parentElement);
          return;
        }
      }
      const smallRaces = ['半身人', '侏儒'];
      if (weaponProps.includes('重型') && smallRaces.includes(playerState.race)) {
        advantageState = 'disadvantage';
        safeToastr('info', `你使用重型武器 (${weapon.name}) 进行攻击，体型过小导致攻击具有劣势。`, '重型武器劣势');
      }
    }

    if (spell) {
      isSpellAttack = true;
      attributeToUse = (playerState.spellcastingAbility?.toLowerCase() || 'intelligence') as typeof attributeToUse;
    } else if (weapon && getEffectiveWeaponProperties(weapon, weaponTemplates).includes('灵巧')) {
      if (playerState.attributes.dexterity.mod > playerState.attributes.strength.mod) {
        attributeToUse = 'dexterity';
      }
    }

    const result = performCheck(
      dc,
      attributeToUse,
      isSpellAttack ? weaponOrSpellName : weapon ? weapon.name : undefined,
      playerState,
      weapon,
      advantageState,
    );
    let rollDetails = `投骰1d20[${result.roll}]`;
    if (result.advantageState === 'advantage') rollDetails += '(优势)';
    if (result.advantageState === 'disadvantage') rollDetails += '(劣势)';
    rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(${result.attributeName})`;
    if (result.proficiencyBonusApplied !== 0) {
      rollDetails += `${result.proficiencyBonusApplied >= 0 ? '+' : ''}${result.proficiencyBonusApplied}(熟练)`;
    }
    const weaponAttackBonusValue = weapon?.magicEffects?.find((eff: MagicEffect) => eff.type === 'ATTACK_BONUS')?.value;
    if (typeof weaponAttackBonusValue === 'number' && weaponAttackBonusValue !== 0) {
      rollDetails += `${weaponAttackBonusValue >= 0 ? '+' : ''}${weaponAttackBonusValue}(武器)`;
    }
    rollDetails += `=${result.total}`;

    let outcome = result.isFumble ? '自动失手' : result.isCritical ? '重击' : result.success ? '攻击命中' : '攻击失手';
    let feedbackDamageString = '';

    if (result.success && weapon) {
      const damageAttributeKey = attributeToUse as keyof PlayerState['attributes'];
      const attributeModifierForDamage = playerState.attributes[damageAttributeKey]?.mod || 0;
      const damageResult = calculateDamageRoll(
        weapon,
        attributeModifierForDamage,
        result.isCritical || false,
        weaponTemplates,
      );
      feedbackDamageString = ` 造成 ${damageResult.totalDamage} 点 ${damageResult.damageType} 伤害 (${damageResult.details})。`;
      const toastMessage = `${weaponOrSpellName} 攻击 ${targetName}: ${result.total} vs DC${dc} -> ${outcome} (${rollDetails}).${feedbackDamageString}`;
      safeToastr(result.success ? 'success' : 'error', toastMessage, '攻击与伤害结果');
      if (mainNarrativeArea) mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
    } else if (result.success && spell) {
      // Handle spell damage
      const spellTemplate = getSpellTemplate(weaponOrSpellName);
      if (spellTemplate && canCastSpell(weaponOrSpellName, playerState)) {
        const spellDamageResult = calculateSpellDamage(weaponOrSpellName, playerState, result.isCritical || false);
        if (spellDamageResult.totalDamage > 0) {
          feedbackDamageString = ` 造成 ${spellDamageResult.totalDamage} 点 ${spellDamageResult.damageType} 伤害 (${spellDamageResult.details})。`;
        }

        // Consume spell slot if it's not a cantrip
        if (spellTemplate.level > 0) {
          consumeSpellSlot(playerState, spellTemplate.level);
          feedbackDamageString += ` 消耗了 ${spellTemplate.level} 环法术位。`;
        }

        const toastMessage = `${weaponOrSpellName} 攻击 ${targetName}: ${result.total} vs DC${dc} -> ${outcome} (${rollDetails}).${feedbackDamageString}`;
        safeToastr(result.success ? 'success' : 'error', toastMessage, '法术攻击与伤害结果');
        if (mainNarrativeArea) mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
      } else {
        const toastMessage = `${weaponOrSpellName} 攻击 ${targetName}: ${result.total} vs DC${dc} -> ${outcome} (${rollDetails}) - 法术槽不足或法术不存在！`;
        safeToastr('error', toastMessage, '法术攻击失败');
        if (mainNarrativeArea) mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
      }
    } else {
      const toastMessage = `${weaponOrSpellName} 攻击 ${targetName}: ${result.total} vs DC${dc} -> ${outcome} (${rollDetails})`;
      safeToastr(result.success ? 'success' : 'error', toastMessage, '攻击结果');
      if (mainNarrativeArea) mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
    }

    let onHitSaveEffectString = '';
    if (result.success && weapon && weapon.magicEffects) {
      weapon.magicEffects.forEach(effect => {
        if (effect.type === 'ON_HIT_EFFECT_SAVE' && effect.value) {
          const saveAttribute = effect.value.saveAttribute as string | undefined;
          const saveDC = effect.value.dc as number | undefined;
          const effectDescriptionOnFail = effect.value.effectOnFail as string | undefined;
          const effectName = effect.notes || '额外效果';
          if (saveAttribute && typeof saveDC === 'number') {
            onHitSaveEffectString += ` 目标 (${targetName}) 还需要进行一次 DC${saveDC} 的 ${saveAttribute} 豁免检定以抵抗 ${
              weapon.name
            } 的 ${effectDescriptionOnFail || effectName}。`;
          }
        }
      });
    }
    checkFeedbackToAI = ` [${outcome} DC${dc} ${attributeToUse}(${weaponOrSpellName}) ${rollDetails}]${feedbackDamageString}${onHitSaveEffectString}`;
  } else if (checkMatch && checkDC > 0) {
    // 使用解析出的检定参数
    if (checkAttribute.toLowerCase().includes('先攻')) {
      const result = performCheck(0, '敏捷', undefined, playerState);
      let rollDetails = `投骰1d20[${result.roll}]${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(敏捷)=${
        result.total
      }`;
      checkFeedbackToAI = ` [先攻检定 ${rollDetails}]`;
      safeToastr('info', `先攻: ${result.total} (${rollDetails})`, '先攻');
      if (mainNarrativeArea)
        mainNarrativeArea.innerHTML += `<p class="check-result"><em>先攻: ${result.total} (${rollDetails})</em></p>`;
    } else {
      const result = performCheck(checkDC, checkAttribute, checkSkill, playerState);
      let rollDetails = `投骰1d20[${result.roll}]`;
      if (result.attributeMod !== 0)
        rollDetails += `${result.attributeMod >= 0 ? '+' : ''}${result.attributeMod}(${result.attributeName})`;
      if (result.proficiencyBonusApplied !== 0)
        rollDetails += `${result.proficiencyBonusApplied >= 0 ? '+' : ''}${result.proficiencyBonusApplied}(熟练)`;
      rollDetails += `=${result.total}`;
      let outcome = result.isFumble
        ? '自动失败'
        : result.isCritical
        ? '大成功'
        : result.success
        ? '检定成功'
        : '检定失败';
      checkFeedbackToAI = ` [${outcome} DC${checkDC} ${result.attributeName}${
        result.skillName ? `(${result.skillName})` : ''
      } ${rollDetails}]`;
      const toastMessage = `${result.attributeName}${result.skillName ? `(${result.skillName})` : ''} 检定: ${
        result.total
      } vs DC${checkDC} -> ${outcome} (${rollDetails})`;
      safeToastr(result.success ? 'success' : 'error', toastMessage, '检定结果');
      if (mainNarrativeArea) mainNarrativeArea.innerHTML += `<p class="check-result"><em>${toastMessage}</em></p>`;
    }
  }

  prompt += `\n玩家刚刚选择了行动： "${choice.text}"${checkFeedbackToAI}\n`;
  prompt += `\n请根据玩家的选择${
    checkFeedbackToAI ? '和检定结果（包括伤害、是否触发后续效果如目标豁免等）' : ''
  }，继续发展剧情。`;
  prompt += `\n\n【重要格式提醒】你的回复必须是包裹在 "查看系统\\n##@@_ADVENTURE_BEGIN_@@##" 和 "##@@_ADVENTURE_END_@@##\\n关闭系统" 内的单一JSON对象。JSON结构需严格遵循AdventureSceneJSON Schema。JSON内部绝对不允许包含任何注释 (如 // 或 /* */)。`;
  prompt += `\n\n【检定格式重要提醒】在playerChoices的text字段中嵌入检定信息时，请严格使用标准格式：[DC{数值} {属性}({技能})] 或 [DC{数值} {属性}]。示例："尝试撬锁[DC15 敏捷(巧手)]"、"说服守卫[DC12 魅力(游说)]"。虽然客户端支持格式变体，但标准格式能确保最佳的解析稳定性。`;
  prompt += `\n\n请特别注意处理由客户端反馈的任何需要目标进行豁免检定的情况，并在你的narrative中描述豁免过程和结果，同时通过variableUpdates更新目标状态。详细规则请参考总纲提示词。`;
  // The full detailed prompt is in adventure_log_v2_ai_prompts.md and will be part of the overall context provided by SillyTavern.
  // The short prompt here is a specific reminder for the current turn.

  if (typeof triggerSlash !== 'function') {
    if (actionChoicesArea)
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
    const waitingMsg = mainNarrativeArea?.querySelector('p > i');
    if (waitingMsg?.parentElement && mainNarrativeArea) mainNarrativeArea.removeChild(waitingMsg.parentElement);
    return;
  }

  try {
    const aiRawResponseWithWrappers = await triggerSlash(`/gen ${prompt}`);
    const narrativeArea = mainNarrativeArea;

    const waitingMsg = narrativeArea?.querySelector('p > i');
    if (waitingMsg?.parentElement && narrativeArea) {
      narrativeArea.removeChild(waitingMsg.parentElement);
    }

    if (aiRawResponseWithWrappers?.trim()) {
      let coreJSONString: string | null = null;
      const newMarkerPatternMatch = aiRawResponseWithWrappers.match(
        /##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/,
      );

      if (newMarkerPatternMatch && newMarkerPatternMatch[1]) {
        coreJSONString = newMarkerPatternMatch[1].trim();
      } else {
        console.warn('Unique markers ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@## not found in AI response.');
      }

      let parsedScene: AdventureSceneJSON | null = null;
      if (coreJSONString) {
        parsedScene = parseAIResponse(coreJSONString);
      }

      if (parsedScene) {
        applySceneData(parsedScene);
        await persistGameState();
        const historyLorebookName = 'dndRPG_history.json';
        let entryKey = `scene_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
        if (parsedScene.sceneTitle && parsedScene.time) {
          const sanitize = (str: string) => str.replace(/[\\/:*?"<>|]/g, '_').replace(/\s+/g, '_');
          const titlePart = sanitize(parsedScene.sceneTitle).substring(0, 40);
          const timePart = sanitize(parsedScene.time).substring(0, 25);
          const uniqueSuffix = Math.random().toString(36).substring(2, 7);
          entryKey = `${titlePart}_${timePart}_${uniqueSuffix}`;
        }
        let entryContent = aiRawResponseWithWrappers;
        const cleanBlockPattern = /查看系统\s*##@@_ADVENTURE_BEGIN_@@##[\s\S]*?##@@_ADVENTURE_END_@@##\s*关闭系统/;
        const cleanMatch = aiRawResponseWithWrappers.match(cleanBlockPattern);
        if (cleanMatch && cleanMatch[0]) {
          entryContent = cleanMatch[0];
        } else {
          safeToastr('warning', '未能从AI回复中提取标准的干净记录块，将保存完整原始回复。', '历史记录警告');
          console.warn(
            '[AdvLogV2 History] Could not extract clean block, saving raw response. Raw:',
            aiRawResponseWithWrappers,
          );
        }
        try {
          const createCommand = `/createentry file="${historyLorebookName}" key="${entryKey}" ${entryContent}`;
          const uidResult = await triggerSlash(createCommand);
          if (uidResult && uidResult.trim() !== '') {
            safeToastr(
              'info',
              `历史场景已保存到 ${historyLorebookName} (Key: ${entryKey}, UID: ${uidResult})`,
              '历史记录',
            );
            const activateCommand = `/setentryfield file="${historyLorebookName}" uid="${uidResult.trim()}" field=constant true`;
            await triggerSlash(activateCommand);
            safeToastr('success', `历史条目 ${entryKey} 已激活 (蓝灯).`, '历史记录');
          } else {
            safeToastr('warning', `保存历史场景 ${entryKey} 到 ${historyLorebookName} 未返回有效UID。`, '历史记录警告');
          }
        } catch (loreError) {
          safeToastr(
            'error',
            `保存或激活历史场景 ${entryKey} 到 ${historyLorebookName} 失败: ${(loreError as Error).message}`,
            '历史记录错误',
          );
          console.error(`Error saving/activating history entry ${entryKey}:`, loreError);
        }
      } else {
        safeToastr('error', 'JSON提取或解析失败，请查看主区域显示的AI原始回复。', '处理错误');
        if (narrativeArea) {
          narrativeArea.innerHTML += `<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (提取或解析失败):</strong>\n${aiRawResponseWithWrappers
            .replace(/</g, '<')
            .replace(/>/g, '>')}</p>`;
        }
        console.error('Failed to extract or parse JSON. Raw AI Response:', aiRawResponseWithWrappers);
        if (!coreJSONString && newMarkerPatternMatch && !newMarkerPatternMatch[1]) {
          console.error('Reason: Markers found, but no content between them or other regex issue.');
        } else if (!coreJSONString) {
          console.error('Reason: Core JSON string could not be extracted (markers not found).');
        } else {
          console.error('Reason: JSON.parse failed. Extracted string was:', coreJSONString);
        }
      }
    } else {
      if (narrativeArea) narrativeArea.innerHTML += "<p style='color:orange;'>AI未返回有效数据。</p>";
      safeToastr('warning', 'AI未返回任何数据。', '无响应');
    }
  } catch (e) {
    const narrativeAreaForError = mainNarrativeArea;
    const waitingMsgOnError = narrativeAreaForError?.querySelector('p > i');
    if (waitingMsgOnError?.parentElement && narrativeAreaForError) {
      narrativeAreaForError.removeChild(waitingMsgOnError.parentElement);
    }
    if (narrativeAreaForError) {
      narrativeAreaForError.innerHTML += `<p style='color:red;'>与AI交互时发生错误: ${(e as Error).message}</p>`;
    }
    safeToastr('error', `与AI交互时发生错误: ${(e as Error).message}`, '交互错误');
  } finally {
    if (actionChoicesArea)
      actionChoicesArea.querySelectorAll('button').forEach(btn => ((btn as HTMLButtonElement).disabled = false));
  }
}

async function handleStartNewGameClick() {
  if (!startNewGameButton || !adventureLogContainer || !startScreenContainer) return;
  startNewGameButton.disabled = true;
  startNewGameButton.textContent = '正在加载角色...';

  const characterDataJson = await loadCharacterDataFromLorebook('PLAYER');
  let newLocalPlayerState = getDefaultPlayerState();

  if (characterDataJson) {
    try {
      const loadedPlayerState: PlayerState = JSON.parse(characterDataJson);
      if (loadedPlayerState?.name && loadedPlayerState.attributes) {
        newLocalPlayerState = loadedPlayerState;
      }
    } catch (e) {
      safeToastr('warning', '角色数据解析失败，使用默认角色。', '新游戏');
    }
  }
  setPlayerState(newLocalPlayerState);
  setCurrentSceneData(null);

  const initialSceneJSON = generateInitialAdventureSceneJSON(playerState);
  applySceneData(initialSceneJSON);

  if (
    typeof getLastMessageId === 'function' &&
    typeof triggerSlash === 'function' &&
    typeof setChatMessages === 'function'
  ) {
    const hostId = getLastMessageId();
    if (hostId !== undefined) {
      await persistGameState();
      const historyLorebookName = 'dndRPG_history.json';
      const entryKey = `scene_init_${Date.now()}`;
      const initialSceneFullResponse = `查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(
        initialSceneJSON,
      )}\n##@@_ADVENTURE_END_@@##\n关闭系统`;
      try {
        const createCommand = `/createentry file="${historyLorebookName}" key="${entryKey}" ${initialSceneFullResponse}`;
        const uidResult = await triggerSlash(createCommand);
        if (uidResult && uidResult.trim() !== '') {
          const activateCommand = `/setentryfield file="${historyLorebookName}" uid="${uidResult.trim()}" field=constant true`;
          await triggerSlash(activateCommand);
        }
      } catch (loreError) {
        safeToastr('error', `保存初始历史场景失败: ${(loreError as Error).message}`, '新游戏错误');
      }
      await persistGameState();
    }
  }

  updatePlayerStatusDisplay(playerState);
  if (startScreenContainer) startScreenContainer.style.display = 'none';
  if (adventureLogContainer) adventureLogContainer.style.display = 'flex';

  if (!characterDataJson) {
    if (typeof getLastMessageId !== 'function' && startNewGameButton) {
      startNewGameButton.disabled = false;
      startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
    }
  } else if (startNewGameButton && !(playerState?.name && playerState.attributes)) {
    startNewGameButton.disabled = false;
    startNewGameButton.textContent = '开始新冒险 (从世界书加载角色)';
  }
}



function handleSpellCasting(): void {
  // safeToastr('info', '开始处理法术施放', 'Debug - 法术施放');

  const spellNameElement = document.getElementById('spell-detail-name');
  const spellSlotSelect = document.getElementById('spell-slot-level') as HTMLSelectElement;

  if (!spellNameElement || !spellSlotSelect) {
    safeToastr('error', '法术施放界面元素缺失', '错误');
    return;
  }

  const spellName = spellNameElement.textContent || '';
  const targetName = getSelectedTargetName();
  const slotLevel = parseInt(spellSlotSelect.value) || 0;

  // 只显示关键的施放信息
  safeToastr('info', `施放 ${spellName} → ${targetName || '无目标'} (${slotLevel}环)`, 'Debug - 法术施放');

  if (!spellName) {
    safeToastr('error', '未选择法术', '错误');
    return;
  }

  // Check if player can cast the spell
  if (!canCastSpell(spellName, playerState)) {
    safeToastr('error', '无法施放此法术：法术槽不足', '法术施放失败');
    return;
  }

  // Close the modal first
  if (spellDetailModal) {
    spellDetailModal.style.display = 'none';
    // safeToastr('info', '关闭法术详情模态框', 'Debug - 法术施放');
  }

  // Create spell casting action and send to AI
  // safeToastr('info', '准备创建法术施放动作', 'Debug - 法术施放');
  createSpellCastingAction(spellName, targetName, slotLevel);
}

function createSpellCastingAction(spellName: string, targetName: string, slotLevel: number): void {
  // safeToastr('info', `创建法术施放动作: ${spellName}, 目标: ${targetName || '无'}, 等级: ${slotLevel}`, 'Debug - 创建动作');

  // Get spell template to determine spell type
  const spellTemplate = getSpellTemplate(spellName);
  if (!spellTemplate) {
    safeToastr('error', `找不到法术模板: ${spellName}`, '错误');
    return;
  }

  // safeToastr('info', `法术类型: ${spellTemplate.attack_type || '无攻击'}, 豁免: ${spellTemplate.save?.attribute || '无'}`, 'Debug - 创建动作');

  // Check for NPCs/enemies in the scene
  const npcsInScene = currentSceneData?.enemies || [];
  const hasNPCs = npcsInScene.length > 0;
  // safeToastr('info', `场景检测: ${hasNPCs ? `有${npcsInScene.length}个NPC` : '无NPC'}`, 'Debug - 创建动作');

  let actionText = '';
  let actionCommand = '';

  // Determine if this spell requires a target and attack/save
  const requiresTarget = spellTemplate.attack_type || spellTemplate.save;
  // safeToastr('info', `法术是否需要目标: ${requiresTarget ? '是' : '否'}`, 'Debug - 创建动作');
  // safeToastr('info', `目标名称: "${targetName}"`, 'Debug - 创建动作');

  if (requiresTarget && hasNPCs && targetName) {
    // Attack spell or save spell with specific target
    if (spellTemplate.attack_type) {
      // Use attack format like weapons: [攻击 目标 使用 法术 DC15]
      const dc = 15; // Default DC, could be calculated based on spell save DC
      actionText = `[攻击 ${targetName} 使用 ${spellName} DC${dc}]`;
      actionCommand = `attack:${spellName}:${targetName}`;
    } else if (spellTemplate.save) {
      // Save-based spell
      actionText = `对 ${targetName} 施放 ${spellName} [${spellTemplate.save.attribute}豁免]`;
      actionCommand = `cast_spell_save:${spellName}:${targetName}:${slotLevel}`;
    }
  } else if (requiresTarget && hasNPCs && !targetName) {
    // Has NPCs but no target specified
    const npcNames = npcsInScene.map(npc => npc.name).join('、');
    actionText = `施放 ${spellName} (场景中有: ${npcNames}，请选择目标)`;
    actionCommand = `cast_spell_choose_target:${spellName}:${slotLevel}`;
  } else if (!requiresTarget || !hasNPCs) {
    // Utility spell or no targets available
    actionText = hasNPCs ? `施放 ${spellName} (无需目标)` : `施放 ${spellName} (场景中无目标)`;
    actionCommand = `cast_spell_utility:${spellName}:${slotLevel}`;
  } else {
    // Fallback
    actionText = `施放 ${spellName}`;
    actionCommand = `cast_spell:${spellName}:${targetName || '无目标'}:${slotLevel}`;
  }

  // Send action to AI
  safeToastr('success', `发送法术动作: ${actionText}`, 'Debug - 发送AI');
  // safeToastr('info', `动作命令: ${actionCommand}`, 'Debug - 发送AI');

  handleActionChoice({
    id: 'spell_cast',
    text: actionText,
    actionCommand: actionCommand,
  });
}



async function onMounted() {
  setTimeout(async () => {
    ensureGlobals();
    initializeDomElements();
    await loadWeaponTemplates();
    await loadSpellTemplates(); // Initialize spell system

    if (
      !startScreenContainer ||
      !startNewGameButton ||
      !adventureLogContainer ||
      !playerStatusArea ||
      !mainNarrativeArea ||
      !actionChoicesArea ||
      !toggleCharSheetButton ||
      !detailedCharacterSheet ||
      // Check new backpack elements
      !toggleBackpackButton ||
      !backpackInterface ||
      !closeBackpackButton ||
      // Check new spellbook elements
      !toggleSpellbookButton ||
      !spellbookInterface ||
      !closeSpellbookButton ||
      !spellDetailModal ||
      !closeSpellDetailButton ||
      !castSpellButton ||
      !cancelSpellButton
    ) {
      console.error('One or more critical UI elements are missing from the DOM.');
      return;
    }

    // Character Sheet Toggle
    toggleCharSheetButton.addEventListener('click', () => {
      if (detailedCharacterSheet && toggleCharSheetButton) {
        const isHidden = detailedCharacterSheet.style.display === 'none';
        detailedCharacterSheet.style.display = isHidden ? 'block' : 'none';
        toggleCharSheetButton.textContent = isHidden ? '隐藏详细角色卡' : '显示详细角色卡';
      }
    });

    // Backpack Toggle
    toggleBackpackButton.addEventListener('click', () => {
      if (backpackInterface && toggleBackpackButton) {
        const isHidden = backpackInterface.style.display === 'none';
        backpackInterface.style.display = isHidden ? 'flex' : 'none'; // Use flex for backpack layout
        if (isHidden) {
          renderCurrencyDisplay(playerState);
          renderEquippedItemsDisplay(playerState);
          renderInventoryDisplay(playerState);
          toggleBackpackButton.textContent = '关闭背包';
        } else {
          toggleBackpackButton.textContent = '打开背包';
        }
      }
    });

    closeBackpackButton.addEventListener('click', () => {
      if (backpackInterface && toggleBackpackButton) {
        backpackInterface.style.display = 'none';
        toggleBackpackButton.textContent = '打开背包';
      }
    });

    // Spellbook Toggle
    toggleSpellbookButton.addEventListener('click', () => {
      // safeToastr('info', '法术书按钮被点击', 'Debug - 法术书按钮');
      if (spellbookInterface && toggleSpellbookButton) {
        const isHidden = spellbookInterface.style.display === 'none';
        // safeToastr('info', `法术书当前状态: ${isHidden ? '隐藏' : '显示'}`, 'Debug - 法术书按钮');
        spellbookInterface.style.display = isHidden ? 'flex' : 'none';
        if (isHidden) {
          // safeToastr('info', '开始渲染法术书界面', 'Debug - 法术书按钮');
          renderSpellbookInterface(playerState);
          toggleSpellbookButton.textContent = '关闭法术书';
        } else {
          toggleSpellbookButton.textContent = '打开法术书';
        }
        // safeToastr('success', `法术书${isHidden ? '已打开' : '已关闭'}`, 'Debug - 法术书按钮');
      } else {
        safeToastr('error', '法术书界面元素未找到', 'Debug - 法术书按钮');
      }
    });

    closeSpellbookButton.addEventListener('click', () => {
      if (spellbookInterface && toggleSpellbookButton) {
        spellbookInterface.style.display = 'none';
        toggleSpellbookButton.textContent = '打开法术书';
      }
    });

    // Spell detail modal controls
    closeSpellDetailButton.addEventListener('click', () => {
      if (spellDetailModal) {
        spellDetailModal.style.display = 'none';
      }
    });

    cancelSpellButton.addEventListener('click', () => {
      if (spellDetailModal) {
        spellDetailModal.style.display = 'none';
      }
    });

    castSpellButton.addEventListener('click', () => {
      handleSpellCasting();
    });

    // Target type selection change handler
    if (spellTargetTypeSelect) {
      spellTargetTypeSelect.addEventListener('change', () => {
        handleTargetTypeChange();
      });
    }

    startNewGameButton.addEventListener('click', handleStartNewGameClick);

    const loadResult = await loadGameState();
    if (loadResult.playerStateLoadedFromMsg && loadResult.sceneDataLoadedFromMsg && currentSceneData) {
      applySceneData(currentSceneData);
      if (startScreenContainer) startScreenContainer.style.display = 'none';
      if (adventureLogContainer) adventureLogContainer.style.display = 'flex';
    } else {
      if (startScreenContainer) startScreenContainer.style.display = 'flex';
      if (adventureLogContainer) adventureLogContainer.style.display = 'none';
      updatePlayerStatusDisplay(playerState);
    }

    // Event listener for backpack interactions (equip, unequip, toggle details)
    if (backpackInterface) {
      backpackInterface.addEventListener('click', async event => {
        const target = event.target as HTMLElement;
        const itemId = target.dataset.itemId;

        if (!itemId) {
          // Check if the click was on an item-related element
          if (target.classList.contains('item-name-toggle')) {
            // Handle clicks on parent if itemId is on child
            const parentLi = target.closest('li[data-item-id]');
            if (parentLi) {
              const actualItemId = (parentLi as HTMLElement).dataset.itemId;
              if (actualItemId) {
                const detailsId = parentLi.closest('#backpack-equipped-items-area')
                  ? `details-equipped-${actualItemId}`
                  : `details-inventory-${actualItemId}`;
                const detailsElement = document.getElementById(detailsId);
                if (detailsElement) {
                  detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
                }
              }
            }
          }
          return;
        }

        if (target.classList.contains('equip-button')) {
          if (equipItem(itemId)) {
            await persistGameState();
            renderEquippedItemsDisplay(playerState);
            renderInventoryDisplay(playerState);
            updatePlayerStatusDisplay(playerState); // Update AC, etc.
          }
        } else if (target.classList.contains('unequip-button')) {
          if (unequipItem(itemId)) {
            await persistGameState();
            renderEquippedItemsDisplay(playerState);
            renderInventoryDisplay(playerState);
            updatePlayerStatusDisplay(playerState); // Update AC, etc.
          }
        } else if (
          target.classList.contains('item-name-toggle') ||
          target.parentElement?.classList.contains('item-name-toggle')
        ) {
          // Determine if the click was on the span or its child (strong/text node)
          const toggleTarget = target.classList.contains('item-name-toggle') ? target : target.parentElement;
          const actualItemId = toggleTarget?.dataset.itemId;

          if (actualItemId) {
            // Try to find if it's an equipped item or inventory item to form the ID
            const detailsId =
              toggleTarget?.closest('ul')?.id === 'backpack-equipped-list'
                ? `details-equipped-${actualItemId}`
                : `details-inventory-${actualItemId}`;
            const detailsElement = document.getElementById(detailsId);
            if (detailsElement) {
              detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
            }
          }
        }
      });
    }

    // Event listener for spellbook interactions
    if (spellbookInterface) {
      spellbookInterface.addEventListener('click', async event => {
        const target = event.target as HTMLElement;
        const spellName = target.dataset.spellName || target.closest('[data-spell-name]')?.getAttribute('data-spell-name');

        // safeToastr('info', `法术书点击事件: ${target.className}`, 'Debug - 法术书');
        // if (spellName) {
        //   safeToastr('info', `选中法术: ${spellName}`, 'Debug - 法术书');
        // }

        if (target.classList.contains('view-spell-button') && spellName) {
          // safeToastr('info', `查看法术详情: ${spellName}`, 'Debug - 法术书');
          showSpellDetailModal(spellName);
        } else if (target.classList.contains('quick-cast-spell-button') && spellName) {
          safeToastr('info', `快速施放: ${spellName}`, 'Debug - 快速施放');
          // Quick cast without opening modal
          if (canCastSpell(spellName, playerState)) {
            // safeToastr('success', `${spellName} 可以施放，开始快速施放`, 'Debug - 快速施放');
            // Get spell template to determine level
            const spellTemplate = getSpellTemplate(spellName);
            if (!spellTemplate) {
              safeToastr('error', `找不到法术模板: ${spellName}`, '错误');
              return;
            }
            const slotLevel = spellTemplate.level;
            // safeToastr('info', `法术等级: ${slotLevel}`, 'Debug - 快速施放');

            // For quick cast, create a simple target selection
            const npcsInScene = currentSceneData?.enemies || [];
            const hasNPCs = npcsInScene.length > 0;
            // 只在有NPC时显示调试信息
            if (hasNPCs) {
              safeToastr('info', `场景中有 ${npcsInScene.length} 个可选目标`, 'Debug - 快速施放');
            }

            let targetName = '';

            // Create target selection options
            const targetOptions = ['自己'];
            if (hasNPCs) {
              npcsInScene.forEach(npc => {
                targetOptions.push(`${npc.name} (${npc.hp.current}/${npc.hp.max} HP)`);
              });
            }
            targetOptions.push('自定义目标');

            // Show selection dialog
            const optionsText = targetOptions.map((option, index) => `${index + 1}. ${option}`).join('\n');
            const selection = prompt(`选择目标:\n${optionsText}\n\n请输入数字 (1-${targetOptions.length}) 或直接输入目标名称:`);

            if (selection) {
              const selectionNum = parseInt(selection);
              if (selectionNum >= 1 && selectionNum <= targetOptions.length) {
                if (selectionNum === 1) {
                  targetName = '自己';
                } else if (selectionNum <= targetOptions.length - 1) {
                  // NPC target
                  const npcIndex = selectionNum - 2;
                  targetName = npcsInScene[npcIndex]?.name || '';
                } else {
                  // Custom target
                  targetName = prompt('请输入自定义目标名称:') || '';
                }
              } else {
                // Direct input
                targetName = selection;
              }
            }

            // 只在选择了目标时显示调试信息
            if (targetName) {
              safeToastr('info', `选择目标: ${targetName}`, 'Debug - 快速施放');
            }

            // Create spell casting action and send to AI
            // safeToastr('info', `创建法术施放动作`, 'Debug - 快速施放');
            createSpellCastingAction(spellName, targetName, slotLevel);
          } else {
            safeToastr('error', '无法施放此法术：法术槽不足', '法术施放失败');
          }
        } else if (target.classList.contains('cast-prepared-spell-button') && spellName) {
          // Cast prepared spell
          // safeToastr('info', `施放已准备法术: ${spellName}`, 'Debug - 已准备法术');
          showSpellDetailModal(spellName);
        } else {
          // safeToastr('warning', `未识别的法术书点击: ${target.className}`, 'Debug - 法术书');
        }
      });

      // Filter event listeners
      if (spellLevelFilter) {
        spellLevelFilter.addEventListener('change', () => {
          renderAvailableSpellsList(playerState);
        });
      }

      if (spellSchoolFilter) {
        spellSchoolFilter.addEventListener('change', () => {
          renderAvailableSpellsList(playerState);
        });
      }
    }
  }, 200);
}

export function initializeAdventureLogApp(): void {
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    onMounted();
  } else {
    document.addEventListener('DOMContentLoaded', onMounted);
  }
}

// 暴露测试函数到全局，用于调试
(window as any).testCheckParsing = testCheckParsing;
(window as any).testSpellSaveLogic = testSpellSaveLogic;
(window as any).quickSaveTest = quickSaveTest;
(window as any).reloadSpellTemplates = reloadSpellTemplates;
