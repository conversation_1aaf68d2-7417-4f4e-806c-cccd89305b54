# Adventure Log v3 文档索引

## 📚 核心文档（主目录）

### 项目概述
- **[README.md](README.md)** - 项目主文档，包含完整的功能介绍和使用指南
- **[PROJECT_SUMMARY_v3.md](PROJECT_SUMMARY_v3.md)** - 项目总结，技术成就和开发历程

### 系统指南
- **[SPELL_LOADING_GUIDE.md](SPELL_LOADING_GUIDE.md)** - 法术加载系统详细指南
- **[adventure_log_v2_ai_prompts.md](adventure_log_v2_ai_prompts.md)** - AI提示词规范和格式

### 维护文档
- **[DOCUMENTATION_CLEANUP_PLAN.md](DOCUMENTATION_CLEANUP_PLAN.md)** - 文档清理计划
- **[DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md)** - 本文档索引

## 🗂️ 分类文档

### 📁 examples/ - 示例和配置
- **[spell_casting_ai_prompt_example.md](examples/spell_casting_ai_prompt_example.md)** - 法术施放AI提示词示例
- **[updated_player_state_example.json](examples/updated_player_state_example.json)** - 玩家状态数据示例

### 📁 docs/archive/ - 历史文档
- **[Development_Summary_20250601.md](docs/archive/Development_Summary_20250601.md)** - 2025年6月1日开发总结
- **[Development_Summary_And_Notes_20250526_Final.md](docs/archive/Development_Summary_And_Notes_20250526_Final.md)** - 2025年5月26日开发总结
- **[PLAYER_OWNED_SPELLS_ONLY_FIX.md](docs/archive/PLAYER_OWNED_SPELLS_ONLY_FIX.md)** - 玩家法术显示修复记录
- **[combat_logic_update_plan.md](docs/archive/combat_logic_update_plan.md)** - 战斗逻辑更新计划

### 📁 docs/tools/ - 工具和脚本
- **[ai_generating_equipment_guide.md](docs/tools/ai_generating_equipment_guide.md)** - AI生成装备指南
- **[法术提取项目总结报告.md](docs/tools/法术提取项目总结报告.md)** - 法术数据提取工具总结

### 📁 docs/reference/ - 参考文档
- **[RPG_Simulator_Detailed_Design.md](docs/reference/RPG_Simulator_Detailed_Design.md)** - RPG模拟器详细设计（需要更新）
- **[武器设计文档.md](docs/reference/武器设计文档.md)** - 武器系统设计参考
- **[weapon_templates.md](docs/reference/weapon_templates.md)** - 武器模板参考

## 🔧 技术文档

### 代码文档
- **[types/index.ts](types/index.ts)** - TypeScript类型定义
- **[spells/index.ts](spells/index.ts)** - 法术系统实现
- **[combat/index.ts](combat/index.ts)** - 战斗系统实现
- **[utils/index.ts](utils/index.ts)** - 工具函数
- **[ui/render.ts](ui/render.ts)** - UI渲染逻辑

### 脚本工具
- **[scripts/](scripts/)** - 法术生成器脚本集合
  - `spell_generator_level_1.js` 到 `spell_generator_level_9.js` - 分级法术生成器
  - `spell_merger.js` - 法术数据合并工具

## 📖 快速导航

### 新用户入门
1. 阅读 [README.md](README.md) 了解项目概述
2. 查看 [SPELL_LOADING_GUIDE.md](SPELL_LOADING_GUIDE.md) 配置法术系统
3. 参考 [examples/](examples/) 中的示例配置

### 开发者指南
1. 查看 [PROJECT_SUMMARY_v3.md](PROJECT_SUMMARY_v3.md) 了解技术架构
2. 阅读 [adventure_log_v2_ai_prompts.md](adventure_log_v2_ai_prompts.md) 了解AI交互格式
3. 参考 [types/index.ts](types/index.ts) 了解数据结构

### 故障排除
1. 查看 [SPELL_LOADING_GUIDE.md](SPELL_LOADING_GUIDE.md) 的故障排除章节
2. 检查 [known_issues/](known_issues/) 目录中的已知问题
3. 参考 [docs/archive/](docs/archive/) 中的历史问题解决记录

### 工具使用
1. 查看 [docs/tools/](docs/tools/) 中的工具文档
2. 使用 [scripts/](scripts/) 中的法术生成器
3. 参考 [examples/](examples/) 中的配置示例

## 🔄 文档维护

### 文档状态
- ✅ **最新** - 主目录中的核心文档
- 📁 **归档** - docs/archive/ 中的历史文档
- 📚 **参考** - docs/reference/ 中的设计文档
- 🔧 **工具** - docs/tools/ 中的工具文档

### 更新原则
1. **核心文档**：随功能更新及时维护
2. **历史文档**：保持不变，仅作参考
3. **参考文档**：定期评估是否需要更新
4. **工具文档**：随工具变化更新

### 文档规范
- 使用Markdown格式
- 包含适当的emoji图标
- 提供清晰的章节结构
- 包含实用的代码示例
- 保持简洁明了的语言

---

## 📝 版本信息

- **文档版本**：v3.0
- **最后更新**：2025年6月4日
- **维护者**：Adventure Log开发团队

**注意**：此索引会随着项目发展持续更新，请定期查看以获取最新的文档信息。
