/**
 * 9环法术生成器
 * 基于AI知识库生成完整的DND5e 9环法术数据
 */

const fs = require('fs');

// 9环法术数据
const LEVEL_9_SPELLS = [
  {
    name_zh: '星界投影',
    name_en: 'Astral Projection',
    level: 9,
    school: '死灵',
    casting_time: '1小时',
    range: '10尺',
    components: ['V', 'S', 'M (每个生物一颗价值1000gp的黄水晶，法术消耗此材料)'],
    duration: '特殊',
    description_short: '将灵魂投射到星界位面。',
    description_long: '你和最多八个自愿生物将你们的星界体投射到星界位面（如果你已经在那里，法术失败并浪费法术位）。物质身体陷入昏迷状态。'
  },
  {
    name_zh: '预见术',
    name_en: 'Foresight',
    level: 9,
    school: '预言',
    casting_time: '1分钟',
    range: '触及',
    components: ['V', 'S', 'M (一根蜂鸟羽毛)'],
    duration: '8小时',
    description_short: '预知危险并获得战斗优势。',
    description_long: '你触摸一个自愿生物并赋予其有限的预知未来的能力。在法术持续时间内，目标无法被突袭，在攻击检定、属性检定和豁免检定上具有优势。'
  },
  {
    name_zh: '异界之门',
    name_en: 'Gate',
    level: 9,
    school: '咒法',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S', 'M (一颗价值5000gp的钻石)'],
    duration: '专注，至多1分钟',
    description_short: '打开通往其他位面的传送门。',
    description_long: '你在射程内一个你选择的空间中咒唤一道传送门。传送门是一个圆形开口，你可以选择其直径为5到20尺。'
  },
  {
    name_zh: '禁锢术',
    name_en: 'Imprisonment',
    level: 9,
    school: '防护',
    casting_time: '1分钟',
    range: '30尺',
    components: ['V', 'S', 'M (根据禁锢类型的特殊成分，价值至少500gp)'],
    duration: '直到解除',
    description_short: '永久囚禁一个生物。',
    description_long: '你创造一个魔法束缚来困住射程内一个你能看见的生物。目标必须成功进行感知豁免，否则被法术束缚。'
  },
  {
    name_zh: '群体医疗术',
    name_en: 'Mass Heal',
    level: 9,
    school: '塑能',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '大量治疗多个目标。',
    description_long: '治疗能量的洪流从你选择的一点流出。选择射程内最多六个生物。每个生物恢复70生命值。这个法术还能结束目标身上的目盲、耳聋和任何疾病。',
    damage: '70',
    damage_type: '治疗'
  },
  {
    name_zh: '流星爆',
    name_en: 'Meteor Swarm',
    level: 9,
    school: '塑能',
    casting_time: '1 动作',
    range: '1英里',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '召唤毁灭性的流星雨。',
    description_long: '炽热的火球从天空坠落到射程内的四个不同点。每个在40尺半径球形区域内的生物必须进行敏捷豁免。',
    damage: '20d6火焰 + 20d6钝击',
    damage_type: '火焰和钝击',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    area_of_effect: { type: '球形', size: '四个40尺半径球形' }
  },
  {
    name_zh: '律令医疗',
    name_en: 'Power Word Heal',
    level: 9,
    school: '塑能',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '立即',
    description_short: '用一个词语完全治愈目标。',
    description_long: '一个治疗之词从你口中说出，极大地强化射程内一个你触摸的生物。如果该生物有100生命值或更少，它被完全治愈。',
    damage: '完全治愈',
    damage_type: '治疗'
  },
  {
    name_zh: '律令死亡',
    name_en: 'Power Word Kill',
    level: 9,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V'],
    duration: '立即',
    description_short: '用一个词语杀死敌人。',
    description_long: '你说出一个力量之词，可以迫使射程内一个你能看见的生物立即死亡。如果你选择的生物有100生命值或更少，它死亡。否则，法术无效。'
  },
  {
    name_zh: '虹光法墙',
    name_en: 'Prismatic Wall',
    level: 9,
    school: '防护',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '10分钟',
    description_short: '创造多层彩色防护墙。',
    description_long: '一面闪烁着彩虹色光芒的垂直不透明墙出现在射程内一个你选择的地点。墙最多90尺长、30尺高、1寸厚。',
    area_of_effect: { type: '墙形', size: '90尺长×30尺高×1寸厚' }
  },
  {
    name_zh: '形体变化',
    name_en: 'Shapechange',
    level: 9,
    school: '变化',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S', 'M (一个价值1500gp的玉环，你必须将其戴在手指上)'],
    duration: '专注，至多1小时',
    description_short: '变为任何生物形态。',
    description_long: '你承担不同生物的形态，持续法术时间。新形态可以是任何挑战等级等于或低于你等级的生物。'
  },
  {
    name_zh: '复仇风暴',
    name_en: 'Storm of Vengeance',
    level: 9,
    school: '咒法',
    casting_time: '1 动作',
    range: '视线',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '召唤毁灭性的风暴。',
    description_long: '一朵雷云在射程内360尺半径的区域上空形成。雷云在法术持续时间内保持存在。当你施展法术时，选择该区域内一点。',
    area_of_effect: { type: '球形', size: '360尺半径' }
  },
  {
    name_zh: '时间停止',
    name_en: 'Time Stop',
    level: 9,
    school: '变化',
    casting_time: '1 动作',
    range: '自身',
    components: ['V'],
    duration: '立即',
    description_short: '短暂停止时间流动。',
    description_long: '你短暂地停止除你之外所有人的时间流动。时间对其他生物停止，而你获得1d4+1回合，在此期间你可以正常行动。'
  },
  {
    name_zh: '完全变形术',
    name_en: 'True Polymorph',
    level: 9,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S', 'M (一滴水银、一块阿拉伯胶和一缕烟)'],
    duration: '专注，至多1小时',
    description_short: '永久改变生物或物体的形态。',
    description_long: '选择射程内一个你能看见的生物或非魔法物体。你将生物变为不同的生物，将生物变为物体，或将物体变为生物。',
    higher_level_cast: {
      per_slot_above_base: '专注满1小时',
      effect: '变化变为永久'
    }
  },
  {
    name_zh: '完全复生术',
    name_en: 'True Resurrection',
    level: 9,
    school: '死灵',
    casting_time: '1小时',
    range: '触及',
    components: ['V', 'S', 'M (一撮圣水和价值25000gp的钻石，法术消耗此材料)'],
    duration: '立即',
    description_short: '复活任何死去的生物。',
    description_long: '你触摸一个死去不超过200年的生物，且其死亡不是因为年老。如果生物的灵魂自由且愿意，生物复活并恢复所有生命值。'
  },
  {
    name_zh: '怪影杀手',
    name_en: 'Weird',
    level: 9,
    school: '幻术',
    casting_time: '1 动作',
    range: '120尺',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '创造多个目标的恐怖幻象。',
    description_long: '利用30尺半径球形区域内生物心中的恐惧，你创造出只有它们能看见的幻象生物。每个在该区域内的生物必须进行感知豁免。',
    save: { attribute: '感知', effect_on_success: '见描述' },
    area_of_effect: { type: '球形', size: '30尺半径' }
  },
  {
    name_zh: '祈愿术',
    name_en: 'Wish',
    level: 9,
    school: '咒法',
    casting_time: '1 动作',
    range: '自身',
    components: ['V'],
    duration: '立即',
    description_short: '实现几乎任何愿望。',
    description_long: '祈愿术是凡人法师能够施展的最强大法术。通过简单地说出你的愿望，你可以改变现实的基础来满足你的愿望。'
  }
];

/**
 * 生成9环法术世界书
 */
function generateLevel9WorldBook() {
  const worldBook = {
    entries: {
      5009: {
        uid: 5009,
        key: ["AI_LEVEL_9_SPELLS", "AI九环法术", "DND5E_AI_LEVEL_9", "AI_SPELLS_LEVEL_9"],
        keysecondary: [],
        comment: `AI生成的DND5e九环法术完整数据 (${LEVEL_9_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_9_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5009
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level9_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成9环法术世界书: ${outputPath} (${LEVEL_9_SPELLS.length}个法术)`);
  
  return LEVEL_9_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 9环法术生成器 ===');
  generateLevel9WorldBook();
  console.log('9环法术世界书生成完成！');
}

module.exports = { LEVEL_9_SPELLS, generateLevel9WorldBook };
