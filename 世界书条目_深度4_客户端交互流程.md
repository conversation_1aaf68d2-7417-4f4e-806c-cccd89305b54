# 🔄 客户端交互流程详解 - 深度4

## 检定与客户端交互流程

### 1. AI提出检定
- **通过选项**: 在playerChoices的text字段中嵌入检定信息
- **通过豁免要求**: 在narrative中描述需要豁免检定的情景

### 2. 客户端处理
- 玩家选择选项或确认豁免
- 客户端解析检定信息，提取DC、属性、技能、目标、武器等
- **本地执行完整检定计算**：
  - 1d20掷骰
  - 应用属性调整值
  - 应用熟练加值
  - 处理武器特性（如灵巧）
  - 判断天然1（自动失手）和天然20（自动成功/重击）

### 3. 客户端反馈示例
**技能检定反馈**：
`玩家选择尝试撬锁，[检定成功 DC15 力量(运动) 投骰1d20[18]+力量调整[+2]+熟练[+3]=23]。`

**攻击检定反馈（命中并造成伤害）**：
`玩家用长剑攻击地精，[重击 DC13 力量(长剑) 投骰1d20[20]+力量调整[+3]+熟练[+2]=25] 造成 12 点挥砍伤害 (基础(1d8)x2: 7 + 属性: 3 + 魔法效果(火焰 1d6): 2)。`

**攻击检定反馈（失手）**：
`玩家用长剑攻击地精，[攻击失手 DC13 力量(长剑) 投骰1d20[5]+力量调整[+3]+熟练[+2]=10]。`

### 4. AI响应要求
- **必须**基于客户端反馈生成新的JSON场景数据
- **剧情描述**: 在narrative中生动描述玩家行动的直接后果
- **伤害处理**: 如果客户端反馈包含伤害信息，AI应自然融入叙述，不需要重复数值
- **豁免处理**: 如果客户端要求目标进行豁免，AI必须描述豁免过程并判定结果
- **状态更新**: 通过variableUpdates更新敌人HP和状态效果

### 5. 敌人行动处理
- **攻击描述**: AI在narrative中描述敌人攻击动作
- **伤害宣告**: 如果敌人攻击玩家，AI负责宣告伤害数值和类型
- **法术豁免**: 如果敌人施放需要玩家豁免的法术，明确给出豁免DC和属性

### 6. 状态更新规则
- **玩家对敌人造成伤害**: 使用variableUpdates减少敌人HP
- **敌人状态改变**: 更新敌人的statusEffects数组
- **确保target字段正确**: "敌人ID:skeleton_1"格式
