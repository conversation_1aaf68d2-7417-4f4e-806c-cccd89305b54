<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>冒险日志</title><style>body{margin:0;font-family:"Merriweather","Georgia",serif;background-image:linear-gradient(to right, rgba(0, 0, 0, 0.25) 0%, transparent 15%, transparent 85%, rgba(0, 0, 0, 0.25) 100%),linear-gradient(rgba(44, 40, 35, 0.5), rgba(44, 40, 35, 0.5)),url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234a433b' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-color:#2c2823;color:#e0e0e0;min-height:100vh;box-sizing:border-box}#adventure-log-container{width:95vw;max-width:700px;background-color:#eaddc7;border:2px solid #5a3d2b;position:relative;border-radius:12px;box-shadow:0 12px 35px rgba(0,0,0,.7),inset 0 0 20px rgba(0,0,0,.2);display:flex;flex-direction:column;padding:18px;box-sizing:border-box;margin:20px auto}#adventure-log-container::before,#adventure-log-container::after{content:"";position:absolute;border-radius:12px;z-index:-1}#adventure-log-container::before{top:-6px;left:-6px;right:-6px;bottom:-6px;border:3px dashed #9c7b60;opacity:.7}#player-status-area{padding-bottom:12px;border-bottom:1px solid #a8886c;margin-bottom:12px;display:flex;flex-direction:column;gap:9px;font-size:.88em}#player-status-area .status-row{display:flex;flex-wrap:wrap;gap:10px;align-items:center;justify-content:space-around}#player-status-area #health,#player-status-area #ac-display,#player-status-area #time,#player-status-area #location{padding:6px 9px;background-color:#d8c8b3;border:1px solid #7a5c44;border-radius:5px;white-space:nowrap;color:#422d1a;box-shadow:0 1px 2px rgba(0,0,0,.1);font-weight:500}#player-status-area button{padding:8px 12px;background-color:#8c6f4f;color:#f0e6d2;border:1px solid #604834;border-radius:6px;cursor:pointer;font-size:.9em;margin-top:5px;text-align:center;width:auto;font-weight:600;transition:background-color .2s,box-shadow .2s,transform .1s;text-shadow:1px 1px 1px rgba(0,0,0,.2)}#player-status-area button:hover{background-color:#7a5c3f;box-shadow:0 0 8px rgba(140,111,79,.8);transform:translateY(-1px)}#player-status-area #toggle-char-sheet-button,#player-status-area #toggle-backpack-button,#player-status-area #toggle-spellbook-button{width:100%;margin-bottom:5px}#detailed-character-sheet{background-color:rgba(216,200,179,.85);padding:14px;border-radius:8px;margin-top:8px;border:1px solid #7a5c44;max-height:40vh;overflow-y:auto;color:#4a321f}#detailed-character-sheet h4{margin-top:14px;margin-bottom:9px;color:#5a3d2b;font-size:1.1em;border-bottom:1px solid #7a5c44;padding-bottom:6px;font-variant:small-caps;position:relative}#detailed-character-sheet h4::before,#detailed-character-sheet h4::after{content:"";position:absolute;bottom:-2px;height:2px;background-color:#9c7b60;width:20px}#detailed-character-sheet h4::before{left:0}#detailed-character-sheet h4::after{right:0}#detailed-character-sheet h4:first-child{margin-top:0}#detailed-character-sheet .status-row{display:flex;flex-wrap:wrap;gap:9px 13px;align-items:center;margin-bottom:9px}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{padding:4px 8px;background-color:#eaddc7;border:1px solid #b89a7c;border-radius:4px;white-space:nowrap;font-size:.92em;color:#4a321f}#detailed-character-sheet ul{list-style-type:"✧ ";padding-left:15px;margin:8px 0 11px 0;font-size:.88em}#detailed-character-sheet ul li{padding:3px 0;border-bottom:1px dotted rgba(122,92,68,.5)}#detailed-character-sheet ul li:last-child{border-bottom:none}#detailed-character-sheet #spell-slots-display{font-size:.88em;padding-left:12px;margin-bottom:11px}.backpack-interface{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.75);z-index:1000;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:20px;box-sizing:border-box}.backpack-interface .backpack-header{display:flex;align-items:center;width:100%;padding-bottom:10px;margin-bottom:15px}.backpack-interface .backpack-header h3{margin:0;color:#e0e0e0;font-variant:small-caps;font-size:1.6em;text-shadow:1px 1px 2px rgba(0,0,0,.7);text-align:center;flex-grow:1}.backpack-interface .backpack-header .close-button{background:none;border:none;font-size:2.2em;color:#bcaaa4;cursor:pointer;padding:0 5px;line-height:1;font-weight:bold;transition:color .2s ease;margin-left:auto;flex-shrink:0}.backpack-interface .backpack-header .close-button:hover{color:#ffcc80}.backpack-interface .backpack-content{position:relative;background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233e2723' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0zM40 40h40v40H40z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-color:#4a3b32;padding:25px;border-radius:12px;border:3px solid #8d6e63;box-shadow:0 15px 40px rgba(0,0,0,.7),inset 0 0 15px rgba(0,0,0,.4);width:90%;max-width:700px;max-height:88vh;overflow-y:auto;color:#d7ccc8}.backpack-interface .backpack-content::-webkit-scrollbar{width:12px}.backpack-interface .backpack-content::-webkit-scrollbar-track{background:#5d4037;border-radius:10px}.backpack-interface .backpack-content::-webkit-scrollbar-thumb{background-color:#8d6e63;border-radius:10px;border:2px solid #5d4037}.backpack-interface .backpack-content .backpack-section{margin-bottom:25px}.backpack-interface .backpack-content .backpack-section:last-child{margin-bottom:0}.backpack-interface .backpack-content .backpack-section h4{margin-top:0;margin-bottom:12px;color:#ffcc80;font-size:1.35em;border-bottom:2px solid #8d6e63;padding-bottom:8px;font-variant:small-caps;text-shadow:1px 1px 2px rgba(0,0,0,.5);letter-spacing:.5px}.backpack-interface .backpack-content .backpack-section p,.backpack-interface .backpack-content .backpack-section ul{margin:0;padding:0;font-size:1em}.backpack-interface .backpack-content .backpack-section ul{list-style:none}.backpack-interface .backpack-content .backpack-section ul li{padding:12px 8px;border-bottom:1px solid #6a4f4b;background-color:hsla(0,0%,100%,.03);border-radius:4px;margin-bottom:8px;transition:background-color .2s ease}.backpack-interface .backpack-content .backpack-section ul li:last-child{border-bottom:none;margin-bottom:0}.backpack-interface .backpack-content .backpack-section ul li:hover{background-color:hsla(0,0%,100%,.07)}.backpack-interface .backpack-content .backpack-section ul li>div:first-child{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.backpack-interface .backpack-content .backpack-section ul li .item-name-toggle{cursor:pointer;font-weight:bold;color:#ffe0b2;flex-grow:1;font-size:1.05em}.backpack-interface .backpack-content .backpack-section ul li .item-name-toggle:hover{color:#ffd54f;text-decoration:none}.backpack-interface .backpack-content .backpack-section ul li .backpack-button{padding:6px 12px;font-size:.9em;color:#fff;border:1px solid rgba(0,0,0,.3);border-radius:5px;cursor:pointer;margin-left:12px;min-width:70px;text-align:center;font-weight:600;text-shadow:1px 1px 1px rgba(0,0,0,.4);transition:background-color .2s,box-shadow .2s,transform .1s;box-shadow:0 2px 3px rgba(0,0,0,.3),inset 0 1px 0 hsla(0,0%,100%,.1)}.backpack-interface .backpack-content .backpack-section ul li .backpack-button:hover{box-shadow:0 3px 5px rgba(0,0,0,.4),inset 0 1px 0 hsla(0,0%,100%,.15);transform:translateY(-1px)}.backpack-interface .backpack-content .backpack-section ul li .backpack-button:active{transform:translateY(0px);box-shadow:0 1px 1px rgba(0,0,0,.3),inset 0 1px 0 rgba(0,0,0,.1)}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.equip-button{background-image:linear-gradient(to bottom, #689f38, #4caf50);border-color:#388e3c}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.equip-button:hover{background-image:linear-gradient(to bottom, #7cb342, #558b2f)}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.unequip-button{background-image:linear-gradient(to bottom, #d32f2f, #c62828);border-color:#b71c1c}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.unequip-button:hover{background-image:linear-gradient(to bottom, #e53935, #b71c1c)}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.use-button{background-image:linear-gradient(to bottom, #0288d1, #0277bd);border-color:#01579b}.backpack-interface .backpack-content .backpack-section ul li .backpack-button.use-button:hover{background-image:linear-gradient(to bottom, #039be5, #01579b)}.backpack-interface .backpack-content .backpack-section ul li .item-details-content{padding:10px;margin-left:20px;background-color:rgba(0,0,0,.15);border:1px solid #5a453c;border-radius:4px;font-size:.95em;line-height:1.5;color:#c5cae9}.backpack-interface .backpack-content .backpack-section ul li .item-details-content p{margin-bottom:6px}.backpack-interface .backpack-content .backpack-section ul li .item-details-content ul{list-style-type:"◈ ";padding-left:18px;margin-top:4px}.backpack-interface .backpack-content .backpack-section ul li .item-details-content ul li.magic-effect{font-size:1em;border-bottom:none;padding:3px 0}.backpack-interface .backpack-content .backpack-section ul li .item-details-content ul li.magic-effect em{color:#80cbc4}.backpack-interface .backpack-content .backpack-section ul li .item-details-content ul li.magic-effect .effect-condition{color:#ffab91;font-style:italic}.backpack-interface .backpack-content .backpack-section ul li.placeholder{font-style:italic;color:#90a4ae;padding:10px 8px;background-color:rgba(0,0,0,0)}.backpack-interface .backpack-content .backpack-section #backpack-currency-area{padding:12px;background-color:rgba(0,0,0,.1);border-radius:6px;border:1px solid #5a453c;margin-top:10px}.backpack-interface .backpack-content .backpack-section #backpack-currency-area p{margin-bottom:6px;font-size:1.05em;color:#e0e0e0}.backpack-interface .backpack-content .backpack-section #backpack-currency-area p:last-child{margin-bottom:0}.backpack-interface .backpack-content .backpack-section #backpack-currency-area p span{font-weight:bold;color:#ffd54f;margin-left:5px}.backpack-interface .backpack-content .backpack-section #backpack-currency-area p.gold-coin::before{content:"💰 "}#main-narrative-area{flex-grow:1;padding:14px;background-color:#faf0e0;border:1px solid #c8a064;border-radius:8px;line-height:1.7;font-size:1.02em;color:#321;margin-bottom:18px;box-shadow:inset 0 0 10px rgba(0,0,0,.1)}#main-narrative-area p{margin-top:0;margin-bottom:1em}#main-narrative-area p:last-child{margin-bottom:0}#main-narrative-area .system-message em{color:#7a522f;font-style:italic;font-weight:500}#main-narrative-area .thought-message i{color:#607d8b;font-style:italic}#main-narrative-area strong{color:#5a3d2b;font-weight:700}#action-choices-area{display:flex;flex-direction:column;gap:9px}#action-choices-area button{padding:13px 17px;background-color:#7a5c3f;color:#f5e8d0;border:1px solid #503d2e;border-bottom:3px solid #503d2e;border-radius:8px;cursor:pointer;font-size:.98em;text-align:left;font-weight:600;font-family:"Merriweather","Georgia",serif;box-shadow:0 2px 3px rgba(0,0,0,.2);transition:background-color .15s ease-out,transform .1s ease-out,box-shadow .15s ease-out,border-bottom-width .1s ease-out;position:relative}#action-choices-area button::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));border-radius:8px;pointer-events:none}#action-choices-area button:hover{background-color:#8c6f4f;border-color:#604834;box-shadow:0 3px 5px rgba(0,0,0,.25);transform:translateY(-2px)}#action-choices-area button:active{background-color:#604834;transform:translateY(1px);border-bottom-width:1px;box-shadow:inset 0 1px 2px rgba(0,0,0,.2)}#start-screen-container{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;padding:20px;box-sizing:border-box;width:100%;height:100%;overflow-y:auto;background-color:#f5e8d0;color:#4a2e00}#start-screen-container h1{font-size:2em;color:#6b4226;margin-bottom:18px;font-variant:small-caps;text-shadow:1px 1px 2px rgba(200,160,100,.5)}#start-screen-container p{font-size:1.05em;color:#5a3811;margin-bottom:22px;max-width:88%;line-height:1.55}#start-screen-container #start-new-game-button{padding:14px 28px;font-size:1.2em;background-color:#6b8e23;color:#f5f5f5;border:2px outset #8fbc8f;border-radius:8px;cursor:pointer;transition:background-color .25s,transform .15s;font-weight:500;text-shadow:1px 1px 1px rgba(0,0,0,.4);box-shadow:0 3px 5px rgba(0,0,0,.3)}#start-screen-container #start-new-game-button:hover{background-color:#556b2f;transform:scale(1.03)}#start-screen-container .start-screen-note{font-size:.78em;color:#705432;margin-top:18px;max-width:82%;line-height:1.45}@media(max-width: 600px){#adventure-log-container{width:100vw;max-width:100vw;border-radius:0;border:none;border-image:none;padding:10px;aspect-ratio:unset}#player-status-area{font-size:.85em;gap:6px;padding-bottom:10px;margin-bottom:10px}#player-status-area .status-row{gap:8px}#player-status-area #health,#player-status-area #ac-display,#player-status-area #time,#player-status-area #location{padding:5px 8px}#detailed-character-sheet{font-size:.85em;padding:10px}#detailed-character-sheet h4{font-size:1em}#detailed-character-sheet .status-row{flex-direction:column;align-items:flex-start}#detailed-character-sheet .status-row div,#detailed-character-sheet .status-row span{width:100%;margin-bottom:4px;text-align:left;font-size:.95em}#detailed-character-sheet ul,#detailed-character-sheet #spell-slots-display{font-size:.95em}.backpack-interface .backpack-content{width:95%;max-height:90vh;padding:15px}.backpack-interface .backpack-content .backpack-section h4{font-size:1.1em}.backpack-interface .backpack-content .backpack-section p,.backpack-interface .backpack-content .backpack-section ul{font-size:.9em}.backpack-interface .backpack-content .backpack-section ul li .backpack-button{font-size:.8em;padding:3px 6px;min-width:50px}#main-narrative-area{font-size:1em;padding:10px;margin-bottom:15px}#action-choices-area button{padding:12px 15px;font-size:.95em}#toggle-char-sheet-button,#player-status-area #toggle-backpack-button,#player-status-area #toggle-spellbook-button{font-size:.9em;padding:8px 12px}}.spellbook-interface{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.8);z-index:1000;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:20px;box-sizing:border-box}.spellbook-interface .spellbook-header{display:flex;align-items:center;width:100%;padding-bottom:10px;margin-bottom:15px}.spellbook-interface .spellbook-header h3{margin:0;color:#e0e0e0;font-variant:small-caps;font-size:1.6em;text-shadow:1px 1px 2px rgba(0,0,0,.7);text-align:center;flex-grow:1}.spellbook-interface .spellbook-header .close-button{background:none;border:none;font-size:2.2em;color:#bcaaa4;cursor:pointer;padding:0 5px;line-height:1;font-weight:bold;transition:color .2s ease;margin-left:auto;flex-shrink:0}.spellbook-interface .spellbook-header .close-button:hover{color:#ffcc80}.spellbook-interface .spellbook-content{background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23283593' fill-opacity='0.1'%3E%3Cpath d='M0 0h40v40H0zM40 40h40v40H40z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");background-color:#2a2a4a;padding:25px;border-radius:12px;border:3px solid #5c6bc0;box-shadow:0 15px 40px rgba(0,0,0,.7),inset 0 0 15px rgba(0,0,0,.4);width:90%;max-width:800px;max-height:88vh;overflow-y:auto;color:#e8eaf6}.spellbook-interface .spellbook-content::-webkit-scrollbar{width:12px}.spellbook-interface .spellbook-content::-webkit-scrollbar-track{background:#3f51b5;border-radius:10px}.spellbook-interface .spellbook-content::-webkit-scrollbar-thumb{background-color:#5c6bc0;border-radius:10px;border:2px solid #3f51b5}.spellbook-interface .spellbook-content .spellbook-section{margin-bottom:25px}.spellbook-interface .spellbook-content .spellbook-section:last-child{margin-bottom:0}.spellbook-interface .spellbook-content .spellbook-section h4{margin-top:0;margin-bottom:12px;color:#c5cae9;font-size:1.35em;border-bottom:2px solid #5c6bc0;padding-bottom:8px;font-variant:small-caps;text-shadow:1px 1px 2px rgba(0,0,0,.5);letter-spacing:.5px}.spellbook-interface .spellbook-content .spellbook-section .spell-filter{display:flex;gap:15px;margin-bottom:15px;align-items:center;flex-wrap:wrap}.spellbook-interface .spellbook-content .spellbook-section .spell-filter label{color:#c5cae9;font-weight:600;font-size:.95em}.spellbook-interface .spellbook-content .spellbook-section .spell-filter select{padding:6px 10px;background-color:#3f51b5;color:#e8eaf6;border:1px solid #5c6bc0;border-radius:4px;font-size:.9em;cursor:pointer}.spellbook-interface .spellbook-content .spellbook-section .spell-filter select:focus{outline:none;border-color:#7986cb;box-shadow:0 0 5px rgba(121,134,203,.5)}.spellbook-interface .spellbook-content .spellbook-section ul{list-style:none;margin:0;padding:0}.spellbook-interface .spellbook-content .spellbook-section ul li{padding:12px;border-bottom:1px solid #3f51b5;background-color:hsla(0,0%,100%,.03);border-radius:6px;margin-bottom:8px;transition:background-color .2s ease}.spellbook-interface .spellbook-content .spellbook-section ul li:last-child{border-bottom:none;margin-bottom:0}.spellbook-interface .spellbook-content .spellbook-section ul li:hover{background-color:hsla(0,0%,100%,.07)}.spellbook-interface .spellbook-content .spellbook-section ul li.disabled{opacity:.6}.spellbook-interface .spellbook-content .spellbook-section ul li.disabled .spell-actions button{opacity:.5;cursor:not-allowed}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;flex-wrap:wrap;gap:10px}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-header .spell-name{font-weight:bold;color:#c5cae9;font-size:1.1em;flex-grow:1}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-header .spell-level{background-color:#5c6bc0;color:#fff;padding:2px 8px;border-radius:12px;font-size:.85em;font-weight:600}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-header .spell-school{background-color:#7986cb;color:#fff;padding:2px 8px;border-radius:12px;font-size:.8em}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-summary{color:#b39ddb;font-size:.95em;margin-bottom:10px;line-height:1.4}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions{display:flex;gap:10px;align-items:center}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button{padding:6px 12px;font-size:.9em;color:#fff;border:1px solid rgba(0,0,0,.3);border-radius:5px;cursor:pointer;font-weight:600;text-shadow:1px 1px 1px rgba(0,0,0,.4);transition:all .2s ease;box-shadow:0 2px 3px rgba(0,0,0,.3)}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button:hover:not(:disabled){box-shadow:0 3px 5px rgba(0,0,0,.4);transform:translateY(-1px)}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.view-spell-button{background:linear-gradient(to bottom, #5c6bc0, #3f51b5);border-color:#303f9f}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.view-spell-button:hover{background:linear-gradient(to bottom, #7986cb, #303f9f)}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.quick-cast-spell-button,.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.cast-prepared-spell-button{background:linear-gradient(to bottom, #ff7043, #d84315);border-color:#bf360c}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.quick-cast-spell-button:hover,.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions button.cast-prepared-spell-button:hover{background:linear-gradient(to bottom, #ff8a65, #bf360c)}.spellbook-interface .spellbook-content .spellbook-section ul li .spell-actions .no-slots{color:#f48fb1;font-style:italic;font-size:.85em}.spellbook-interface .spellbook-content .spellbook-section ul li.prepared-spell{border-left:4px solid #4caf50}.spellbook-interface .spellbook-content .spellbook-section ul li.prepared-spell .spell-name{color:#a5d6a7}.spellbook-interface .spellbook-content .spellbook-section ul li.placeholder{font-style:italic;color:#9575cd;padding:15px;text-align:center;background-color:rgba(0,0,0,0)}.spellbook-interface .spellbook-content .spellbook-section .spell-slot-level{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;background-color:hsla(0,0%,100%,.05);border-radius:4px;margin-bottom:6px;border-left:3px solid #5c6bc0}.spellbook-interface .spellbook-content .spellbook-section .spell-slot-level .spell-slot-label{font-weight:600;color:#c5cae9}.spellbook-interface .spellbook-content .spellbook-section .spell-slot-level .spell-slot-count{font-weight:bold;color:#81c784}.spellbook-interface .spellbook-content .spellbook-section .no-spell-slots{text-align:center;color:#f48fb1;font-style:italic;padding:15px}.spell-modal{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.85);z-index:1100;display:flex;justify-content:center;align-items:center;padding:20px;box-sizing:border-box}.spell-modal .spell-modal-content{background:linear-gradient(135deg, #2a2a4a 0%, #1a1a3a 100%);border:3px solid #5c6bc0;border-radius:12px;box-shadow:0 20px 50px rgba(0,0,0,.8);width:90%;max-width:600px;max-height:90vh;overflow-y:auto;color:#e8eaf6}.spell-modal .spell-modal-content .spell-modal-header{display:flex;justify-content:space-between;align-items:center;padding:20px 25px 15px;border-bottom:2px solid #5c6bc0}.spell-modal .spell-modal-content .spell-modal-header h3{margin:0;color:#c5cae9;font-size:1.5em;font-variant:small-caps;text-shadow:1px 1px 2px rgba(0,0,0,.5)}.spell-modal .spell-modal-content .spell-modal-header .close-button{background:none;border:none;font-size:2em;color:#bcaaa4;cursor:pointer;padding:0;line-height:1;transition:color .2s ease}.spell-modal .spell-modal-content .spell-modal-header .close-button:hover{color:#ffcc80}.spell-modal .spell-modal-content .spell-modal-body{padding:20px 25px}.spell-modal .spell-modal-content .spell-modal-body .spell-basic-info{margin-bottom:20px}.spell-modal .spell-modal-content .spell-modal-body .spell-basic-info p{margin:8px 0;display:flex;align-items:center}.spell-modal .spell-modal-content .spell-modal-body .spell-basic-info p strong{color:#c5cae9;min-width:80px;margin-right:10px}.spell-modal .spell-modal-content .spell-modal-body .spell-basic-info p span{color:#b39ddb}.spell-modal .spell-modal-content .spell-modal-body .spell-description{margin-bottom:20px}.spell-modal .spell-modal-content .spell-modal-body .spell-description h4{color:#c5cae9;margin-bottom:10px;font-size:1.2em}.spell-modal .spell-modal-content .spell-modal-body .spell-description p{line-height:1.6;color:#e8eaf6}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options h4{color:#c5cae9;margin-bottom:15px;font-size:1.2em}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection{margin-bottom:15px;display:flex;align-items:center;gap:10px}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection label,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection label{color:#c5cae9;font-weight:600;min-width:80px}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection select,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection input,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection select,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection input{padding:8px 12px;background-color:#3f51b5;color:#e8eaf6;border:1px solid #5c6bc0;border-radius:4px;font-size:.95em;flex-grow:1}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection select:focus,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-slot-selection input:focus,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection select:focus,.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection input:focus{outline:none;border-color:#7986cb;box-shadow:0 0 5px rgba(121,134,203,.5)}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection{flex-direction:column;align-items:flex-start}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection #custom-target-input{width:100%;margin-top:8px}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection #custom-target-input input{width:100%;background-color:#424242;border-color:#616161}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection #custom-target-input input::placeholder{color:#9e9e9e}.spell-modal .spell-modal-content .spell-modal-body .spell-casting-options .spell-target-selection #custom-target-input input:focus{background-color:#3f51b5;border-color:#7986cb}.spell-modal .spell-modal-content .spell-modal-footer{padding:15px 25px 20px;border-top:1px solid #5c6bc0;display:flex;gap:15px;justify-content:flex-end}.spell-modal .spell-modal-content .spell-modal-footer button{padding:10px 20px;font-size:1em;font-weight:600;border:none;border-radius:6px;cursor:pointer;transition:all .2s ease;text-shadow:1px 1px 1px rgba(0,0,0,.3)}.spell-modal .spell-modal-content .spell-modal-footer button.cast-spell-button{background:linear-gradient(to bottom, #ff7043, #d84315);color:#fff}.spell-modal .spell-modal-content .spell-modal-footer button.cast-spell-button:hover{background:linear-gradient(to bottom, #ff8a65, #bf360c);transform:translateY(-1px);box-shadow:0 4px 8px rgba(0,0,0,.3)}.spell-modal .spell-modal-content .spell-modal-footer button.cancel-button{background:linear-gradient(to bottom, #757575, #424242);color:#fff}.spell-modal .spell-modal-content .spell-modal-footer button.cancel-button:hover{background:linear-gradient(to bottom, #9e9e9e, #212121);transform:translateY(-1px);box-shadow:0 4px 8px rgba(0,0,0,.3)}
</style></head><body><div id="start-screen-container"><h1>冒险日志</h1><p>准备好开始一段新的旅程了吗？</p><button id="start-new-game-button">开始新冒险 (从世界书加载角色)</button><p class="start-screen-note">注意：开始新冒险将尝试从名为 "RPG_Modules_Test.json" 的世界书中的 "PLAYER" 条目加载角色数据。如果已有进行中的游戏，它可能会被新游戏覆盖（取决于当前消息内容）。</p></div><div id="adventure-log-container" style="display:none"><div id="player-status-area"><div class="status-row"><div id="health">生命值: ...</div><div id="ac-display">AC: ...</div><div id="time">时间: ...</div><div id="location">地点: ...</div></div><button id="toggle-char-sheet-button">显示/隐藏详细角色卡</button> <button id="toggle-backpack-button">打开背包</button> <button id="toggle-spellbook-button">打开法术书</button><div id="detailed-character-sheet" style="display:none"><h4>角色信息</h4><div class="status-row"><div id="char-name-display">角色名: ...</div><div id="char-race-class-display">种族/职业: .../...</div><div id="char-level-display">等级: ...</div></div><h4>属性</h4><div class="status-row attributes-display"><div id="attr-str-display">力量: ..(..)</div><div id="attr-dex-display">敏捷: ..(..)</div><div id="attr-con-display">体质: ..(..)</div><div id="attr-int-display">智力: ..(..)</div><div id="attr-wis-display">感知: ..(..)</div><div id="attr-cha-display">魅力: ..(..)</div></div><h4>状态</h4><div class="status-row simple-list-display"><span>经验: <span id="exp-display">...</span></span> <span>力竭: <span id="exhaustion-display">...</span></span></div><h4>熟练项</h4><ul id="proficiencies-display"><li>...</li></ul><h4>技能 (熟练)</h4><ul id="skills-display"><li>...</li></ul><h4>法术槽</h4><div id="spell-slots-display">...</div><h4>已准备法术</h4><ul id="equipped-spells-display"><li>...</li></ul><h4>当前任务</h4><ul id="active-quests-display"><li>...</li></ul></div></div><div id="main-narrative-area"><p>冒险即将开始...</p></div><div id="action-choices-area"></div></div><div id="backpack-interface" class="backpack-interface" style="display:none"><div class="backpack-header"><h3>我的背包</h3><button id="close-backpack-button" class="close-button">&times;</button></div><div class="backpack-content"><div id="backpack-currency-area" class="backpack-section"><h4>货币</h4><p>金币: <span id="backpack-currency-gold">0</span></p><p>银币: <span id="backpack-currency-silver">0</span></p><p>铜币: <span id="backpack-currency-copper">0</span></p></div><div id="backpack-equipped-items-area" class="backpack-section"><h4>已装备</h4><ul id="backpack-equipped-list"><li class="placeholder">无已装备物品。</li></ul></div><div id="backpack-inventory-items-area" class="backpack-section"><h4>物品栏</h4><ul id="backpack-inventory-list"><li class="placeholder">物品栏为空。</li></ul></div></div></div><div id="spellbook-interface" class="spellbook-interface" style="display:none"><div class="spellbook-header"><h3>法术书</h3><button id="close-spellbook-button" class="close-button">&times;</button></div><div class="spellbook-content"><div id="spell-slots-area" class="spellbook-section"><h4>法术槽</h4><div id="spellbook-spell-slots-display">...</div></div><div id="prepared-spells-area" class="spellbook-section"><h4>已准备法术</h4><ul id="spellbook-prepared-spells-list"><li class="placeholder">无已准备法术。</li></ul></div><div id="available-spells-area" class="spellbook-section"><h4>可用法术</h4><div class="spell-filter"><label for="spell-level-filter">法术等级:</label> <select id="spell-level-filter"><option value="all">全部</option><option value="0">戏法</option><option value="1">1环</option><option value="2">2环</option><option value="3">3环</option><option value="4">4环</option><option value="5">5环</option><option value="6">6环</option><option value="7">7环</option><option value="8">8环</option><option value="9">9环</option></select> <label for="spell-school-filter">学派:</label> <select id="spell-school-filter"><option value="all">全部</option><option value="塑能">塑能</option><option value="咒法">咒法</option><option value="预言">预言</option><option value="惑控">惑控</option><option value="塑能">塑能</option><option value="幻术">幻术</option><option value="死灵">死灵</option><option value="变化">变化</option><option value="防护">防护</option></select></div><ul id="spellbook-available-spells-list"><li class="placeholder">正在加载法术...</li></ul></div></div></div><div id="spell-detail-modal" class="spell-modal" style="display:none"><div class="spell-modal-content"><div class="spell-modal-header"><h3 id="spell-detail-name">法术名称</h3><button id="close-spell-detail-button" class="close-button">&times;</button></div><div class="spell-modal-body"><div class="spell-basic-info"><p><strong>等级:</strong> <span id="spell-detail-level">-</span></p><p><strong>学派:</strong> <span id="spell-detail-school">-</span></p><p><strong>施法时间:</strong> <span id="spell-detail-casting-time">-</span></p><p><strong>射程:</strong> <span id="spell-detail-range">-</span></p><p><strong>成分:</strong> <span id="spell-detail-components">-</span></p><p><strong>持续时间:</strong> <span id="spell-detail-duration">-</span></p></div><div class="spell-description"><h4>描述</h4><p id="spell-detail-description">法术描述...</p></div><div class="spell-casting-options" id="spell-casting-options"><h4>施法选项</h4><div class="spell-slot-selection"><label for="spell-slot-level">使用法术槽:</label> <select id="spell-slot-level"></select></div><div class="spell-target-selection"><label for="spell-target-type">目标选择:</label> <select id="spell-target-type"><option value="self">自己</option><option value="custom">自定义目标</option></select><div id="custom-target-input" style="display:none;margin-top:8px"><input id="spell-target-custom" placeholder="输入目标名称"></div></div></div></div><div class="spell-modal-footer"><button id="cast-spell-button" class="cast-spell-button">施放法术</button> <button id="cancel-spell-button" class="cancel-button">取消</button></div></div></div><script>function e(e,t,n){try{if("object"!=typeof window.toastr&&"undefined"!=typeof parent&&"object"==typeof parent.toastr&&(window.toastr=parent.toastr),"object"==typeof toastr&&null!==toastr&&"function"==typeof toastr[e])toastr[e](t,n);else{("error"===e?console.error:"warning"===e?console.warn:console.log)(`[AdvLog Toastr Fallback - ${e}] ${n?n+": ":""}${t}`)}}catch(e){console.error(`[AdvLog] safeToastr Error: ${e.message}`)}}function t(e){return e>=17?6:e>=13?5:e>=9?4:e>=5?3:2}function n(e){const t=e.match(/(\d+)d(\d+)(?:\s*([+-])\s*(\d+))?/);if(t){const e=parseInt(t[1],10),n=parseInt(t[2],10);let a=0;return t[3]&&t[4]&&(a=parseInt(t[4],10),"-"===t[3]&&(a=-a)),{count:e,die:n,modifier:a}}return null}function a(e,t){let n=0;for(let a=0;a<e;a++)n+=Math.floor(Math.random()*t)+1;return n}function s(e,t){if(!e)return[];let n=[];if(e.baseItemName&&t&&t.length>0){const a=t.find((t=>t.name_zh===e.baseItemName||t.name_en===e.baseItemName));a&&a.properties&&(n=[...a.properties])}else e.properties&&(n=[...e.properties]);return e.magicEffects&&e.magicEffects.forEach((e=>{"WEAPON_PROPERTY_GRANT"===e.type&&"string"==typeof e.value?n.includes(e.value)||n.push(e.value):"WEAPON_PROPERTY_MODIFY"===e.type&&"object"==typeof e.value&&"remove"===e.value.action&&"string"==typeof e.value.propertyToRemove&&(n=n.filter((t=>t!==e.value.propertyToRemove)))})),[...new Set(n)]}function i(e,n,a=1,s=!1){const i=Math.floor(20*Math.random())+1;let o=0;const l=Math.max(-1,Math.floor((a-1)/4));switch(n.toLowerCase()){case"体质":case"constitution":o=l+1;break;case"感知":case"wisdom":case"敏捷":case"dexterity":case"力量":case"strength":default:o=l;break;case"魅力":case"charisma":case"智力":case"intelligence":o=Math.max(-2,l-1)}const r=s?t(a):0,c=i+o+r,d=20===i,p=1===i;return{success:d||!p&&c>=e,roll:i,modifier:o+r,total:c,dc:e,attribute:n,isCritical:d,isFumble:p}}function o(e,n,a,s,i,o="normal"){let l=Math.floor(20*Math.random())+1,r=l;if("advantage"===o){const e=Math.floor(20*Math.random())+1;r=Math.max(l,e)}else if("disadvantage"===o){const e=Math.floor(20*Math.random())+1;r=Math.min(l,e)}let c=0,d=0,p=0;const u=n.toLowerCase();if(s.attributes[u]&&(c=s.attributes[u].mod),a){const e=s.skills.find((e=>e.name.toLowerCase()===a.toLowerCase()));if(e?.proficient)d=t(s.level);else{if(s.proficiencies.find((e=>e===a)))d=t(s.level);else{s.proficiencies.find((e=>e.toLowerCase().includes(a.toLowerCase())&&e.toLowerCase().includes(n.toLowerCase())))&&(d=t(s.level))}}}!a&&s.proficiencies.includes(`${n}豁免`)&&(d=t(s.level)),i&&i.magicEffects&&i.magicEffects.forEach((e=>{"ATTACK_BONUS"===e.type&&"number"==typeof e.value&&(p+=e.value)}));const m=r+c+d+p;let f=m>=e;const g=20===r,y=1===r;return g&&e>0&&(f=!0),y&&(f=!1),{success:f,roll:r,attributeMod:c,proficiencyBonusApplied:d,total:m,dc:e,attributeName:n,skillName:a,isCritical:g,isFumble:y,advantageState:o}}let l=m(),r=[],c=null,d=null;const p="\x3c!-- PLAYER_STATE_START --\x3e",u="\x3c!-- PLAYER_STATE_END --\x3e";function m(){const e=[{id:"default_backpack_01",name:"背包",quantity:1,description:"一个普通的背包。"},{id:"default_tinderbox_01",name:"火绒盒",quantity:1,description:"用于生火。"},{id:"default_rations_01",name:"口粮",quantity:2,description:"一天的食物"}],t=e.find((e=>"default_backpack_01"===e.id));t&&(t.type="容器");const n=e.find((e=>"default_tinderbox_01"===e.id));n&&(n.type="工具");const a=e.find((e=>"default_rations_01"===e.id));return a&&(a.type="消耗品"),{name:"新冒险者",race:"人类",class:"平民",level:1,exp:0,hp:{current:10,max:10},ac:10,currency:{gold:10,silver:0,copper:0},attributes:{strength:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},dexterity:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},constitution:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},intelligence:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},wisdom:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0},charisma:{base:10,race_bonus:0,modifier_bonus:0,final:10,mod:0}},proficiencies:["匕首"],skills:[],spellSlots:{1:{current:2,max:2},2:{current:1,max:1},3:{current:1,max:1}},equippedSpells:[{name:"火焰箭",level:0},{name:"光亮术",level:0},{name:"魔法飞弹",level:1},{name:"治疗轻伤",level:1},{name:"火球术",level:3}],equipment:[{id:"default_clothing_01",name:"平民服装",type:"衣物",equipped:!0,details:"一套普通的平民服装。"},{id:"default_dagger_01",name:"匕首",baseItemName:"匕首",type:"武器",equipped:!0,properties:["灵巧","轻型"],details:"一把标准的匕首。"}],inventory:e,activeQuests:[],exhaustion:0,time:"第一天 清晨",currentLocation:"旅途的起点",spellcastingAbility:"INT",lastProcessedSceneUuid:null}}async function f(){if(null===d||"function"!=typeof setChatMessages)return;let t=`${p}\n${JSON.stringify(l,null,2)}\n${u}\n\n`;c&&(t+=`查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(c)}\n##@@_ADVENTURE_END_@@##\n关闭系统`);try{await setChatMessages([{message_id:d,message:t}],{refresh:"affected"})}catch(t){e("error",`Failed to persist game state: ${t.message}`,"Persistence Error")}}function g(t){try{const n=JSON.parse(t);return n&&n.sceneType&&n.sceneTitle&&n.narrative&&n.playerChoices?n:(console.error("[AdvLog] Invalid AdventureSceneJSON structure:",n),e("error","parseAIResponse: Invalid AdventureSceneJSON structure.","Parse Debug"),null)}catch(n){return console.error("[AdvLog] Failed to parse AdventureSceneJSON string:",t,n),e("error",`parseAIResponse: JSON.parse error - ${n.message}`,"Parse Debug"),null}}function y(e){l=e}function $(e){c=e}function h(t){const n=l.equipment.findIndex((e=>e.id===t));if(-1===n)return e("warning",`物品 ${t} 未装备。`,"卸装失败"),!1;const a=l.equipment.splice(n,1)[0],s=l.inventory.find((e=>e.id&&e.id===a.id||e.name===a.name));if(s)s.quantity+=1,a.type&&(s.type=a.type),s.description=a.details||s.description,s.baseItemName=a.baseItemName||s.baseItemName,s.properties=a.properties||s.properties,s.magicEffects=a.magicEffects||s.magicEffects;else{const e={id:a.id,name:a.name,description:a.details,baseItemName:a.baseItemName,properties:a.properties,magicEffects:a.magicEffects,quantity:1};a.type&&(e.type=a.type),l.inventory.push(e)}return e("success",`${a.name} 已卸下并放入物品栏。`,"卸装成功"),!0}let b=[];function v(){return b}async function E(){await I()}async function _(t="Spell_Library.json",n="TEST_SPELL"){if(e("info",`开始测试世界书: ${t}`,"世界书测试"),"function"==typeof triggerSlash)try{const a=`/findentry file="${t}" "${n}"`;e("info",`执行命令: ${a}`,"世界书测试");const s=await triggerSlash(a);if(void 0===s)return void e("error",`❌ 世界书文件 ${t} 不存在或无法访问`,"世界书测试");if(""===s.trim()||"[]"===s.trim())return void e("warning",`✅ 世界书 ${t} 存在，但未找到关键字 ${n}`,"世界书测试");let i=null;try{const e=JSON.parse(s);i=Array.isArray(e)&&e.length>0?e[0].toString():e.toString()}catch(e){i=s.trim()}if(i){const n=`/getentryfield file="${t}" field=content "${i}"`,a=await triggerSlash(n);a&&""!==a.trim()?e("success",`✅ 成功获取内容，长度: ${a.length} 字符`,"世界书测试"):e("warning","⚠️ 条目存在但内容为空","世界书测试")}else e("error",`❌ 无法解析UID: ${s}`,"世界书测试")}catch(t){e("error",`❌ 连接失败: ${t.message}`,"世界书测试")}else e("error","triggerSlash 函数不可用","世界书测试")}async function S(t,n){if("function"!=typeof triggerSlash)return null;const a="Spell_Library.json";try{const n=`/findentry file="${a}" "${t}"`,s=await triggerSlash(n);if(void 0===s)return"AI_ALL_SPELLS"===t&&e("error",`❌ 世界书文件 ${a} 不存在`,"世界书检测"),null;if(!s?.trim()||"[]"===s.trim())return"AI_ALL_SPELLS"===t&&e("warning",`✅ 世界书 ${a} 存在，但未找到 ${t}`,"世界书检测"),null;let i=null;try{const e=JSON.parse(s);i=Array.isArray(e)&&e.length>0?e[0].toString():e.toString()}catch(n){i=s.trim(),"AI_ALL_SPELLS"===t&&e("info",`🔧 使用原始UID: ${i}`,"世界书检测")}if(!i)return"AI_ALL_SPELLS"===t&&e("error",`❌ 无法解析UID: ${s}`,"世界书检测"),null;const o=`/getentryfield file="${a}" field=content "${i}"`,l=await triggerSlash(o);return l&&""!==l.trim()?("AI_ALL_SPELLS"===t&&e("success",`✅ 成功从世界书获取 ${t}`,"世界书检测"),l):null}catch(e){return null}}const L="spellTemplates_v1.0";async function I(){try{const e=localStorage.getItem(L);if(e)try{const t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)return void(b=t)}catch(e){localStorage.removeItem(L)}await C()}catch(t){b=[],e("warning","⚠️ 法术加载失败，使用默认模板","法术系统")}}async function C(){const t=await S("AI_ALL_SPELLS");if(t)try{const n=JSON.parse(t);if(Array.isArray(n)&&n.length>0)return b=n,localStorage.setItem(L,JSON.stringify(b)),void e("success",`✅ 从世界书加载 ${b.length} 个法术并已缓存`,"法术系统")}catch(e){}const n=await async function(){const e=[],t=["AI_CANTRIPS","AI_LEVEL_1_SPELLS","AI_LEVEL_2_SPELLS","AI_LEVEL_3_SPELLS","AI_LEVEL_4_SPELLS","AI_LEVEL_5_SPELLS","AI_LEVEL_6_SPELLS","AI_LEVEL_7_SPELLS","AI_LEVEL_8_SPELLS","AI_LEVEL_9_SPELLS"];for(const n of t)try{const t=await S(n);if(t){const n=JSON.parse(t);Array.isArray(n)&&e.push(...n)}}catch(e){}return e}();if(n.length>0)return b=n,localStorage.setItem(L,JSON.stringify(b)),void e("success",`✅ 分级加载 ${b.length} 个法术并已缓存`,"法术系统");b=[],e("info",`📋 使用默认法术模板，共 ${b.length} 个法术`,"法术系统")}function w(e){if(!b||0===b.length)return null;return b.find((t=>t.name_zh===e||t.name_en===e))||null}function D(e,t,n){const a=w(e);if(!a)return!1;const s=n||a.level;if(0===s)return!0;if(!t.spellSlots)return!1;const i=t.spellSlots[s.toString()];return i&&i.current>0}function A(e,t){if(0===t)return!0;const n=e.spellSlots[t.toString()];return!!(n&&n.current>0)&&(n.current--,!0)}"undefined"!=typeof window&&(window.spellSystem={getSpellTemplates:v,reloadSpellTemplates:E,clearSpellCache:async function(){localStorage.removeItem(L),e("info","🗑️ 已清除法术缓存","法术系统"),await C()},testLorebookConnection:async function(){await _("Spell_Library.json","AI_ALL_SPELLS")},testSpecificKey:_});let T=null,N=null,x=null,k=null,M=null,B=null,q=null,P=null,O=null,H=null,j=null,U=null,R=null,J=null,G=null,V=null,z=null,F=null,W=null,Y=null,K=null,Q=null,X=null,Z=null,ee=null,te=null,ne=null,ae=null,se=null,ie=null,oe=null,le=null,re=null,ce=null,de=null,pe=null,ue=null,me=null,fe=null,ge=null,ye=null,$e=null,he=null,be=null,ve=null,Ee=null,_e=null,Se=null,Le=null,Ie=null,Ce=null,we=null,De=null,Ae=null,Te=null,Ne=null,xe=null,ke=null,Me=null,Be=null,qe=null,Pe=null;function Oe(e){z&&(z.textContent=`${e.currency?.gold||0}`),F&&(F.textContent=`${e.currency?.silver||0}`),W&&(W.textContent=`${e.currency?.copper||0}`)}function He(e){if(!K)return;const t=e.equipment.filter((e=>e.equipped));if(t.length>0){let e='<ul id="backpack-equipped-list">';t.forEach((t=>{const n=t.id||`equipped_${Date.now()}_${Math.random().toString(36).substring(2,7)}`;e+=`<li class="equipment-item" data-item-id="${n}">`,e+="<div>",e+=`<span class="item-name-toggle" data-item-id="${n}" role="button" tabindex="0"><strong>${t.name}</strong> (${t.type})</span>`,e+=`<button class="backpack-button unequip-button" data-item-id="${n}">卸下</button>`,e+="</div>",e+=`<div class="item-details-content" id="details-equipped-${n}" style="display: none;">`;let a="";if(t.baseItemName){const e=r.find((e=>e.name_zh===t.baseItemName||e.name_en===t.baseItemName));e&&(a+=`<p><em>模板: ${t.baseItemName} (${e.damage} ${e.damageType})</em></p>`,e.properties&&e.properties.length>0&&(a+=`<p>&nbsp;&nbsp;<em>基础属性: ${e.properties.join(", ")}</em></p>`))}if(!a.includes("基础:")&&t.damage&&t.damageType&&(a+=`<p><em>基础: ${t.damage} ${t.damageType}</em></p>`),t.properties&&t.properties.length>0&&"武器"===t.type&&!a.includes("基础属性")&&(a+=`<p><em>属性: ${t.properties.join(", ")}</em></p>`),t.details&&(a+=`<p><span class="item-description">描述: ${t.details.replace(/\n/g,"<br />&nbsp;&nbsp;")}</span></p>`),"武器"===t.type){const e=s(t,r);e.length>0&&(a+=`<p><em>有效属性: ${e.join(", ")}</em></p>`)}t.magicEffects&&t.magicEffects.length>0&&(a+="<p>魔法效果:<ul>",t.magicEffects.forEach((e=>{a+=`<li class="magic-effect"><em>${e.notes||e.type}:</em> `,"object"==typeof e.value&&null!==e.value?a+=Object.entries(e.value).map((([e,t])=>`${e}: ${t}`)).join(", "):a+=`${e.value}`,e.condition&&(a+=` <span class="effect-condition">(${e.condition})</span>`),a+="</li>"})),a+="</ul></p>"),e+=a||"<p>无更多详情。</p>",e+="</div></li>"})),e+="</ul>",K.innerHTML=e}else K.innerHTML='<ul id="backpack-equipped-list"><li class="placeholder">无已装备物品。</li></ul>'}function je(e){if(X)if(e.inventory&&e.inventory.length>0){let t='<ul id="backpack-inventory-list">';e.inventory.forEach((e=>{const n=e.id||`inventory_${Date.now()}_${Math.random().toString(36).substring(2,7)}`,a=e.type&&!["消耗品","杂物","材料","容器"].includes(e.type);t+=`<li class="inventory-item" data-item-id="${n}">`,t+="<div>",t+=`<span class="item-name-toggle" data-item-id="${n}" role="button" tabindex="0">${e.name} x${e.quantity}</span>`,a&&(t+=`<button class="backpack-button equip-button" data-item-id="${n}">装备</button>`),t+="</div>",t+=`<div class="item-details-content" id="details-inventory-${n}" style="display: none;">`;let s="";if(e.description&&(s+=`<p><span class="item-description">描述: ${e.description.replace(/\n/g,"<br />&nbsp;&nbsp;")}</span></p>`),e.baseItemName){const t=r.find((t=>t.name_zh===e.baseItemName||t.name_en===e.baseItemName));t&&(s+=`<p><em>模板: ${e.baseItemName} (${t.damage} ${t.damageType})</em></p>`,t.properties&&t.properties.length>0&&(s+=`<p>&nbsp;&nbsp;<em>基础属性: ${t.properties.join(", ")}</em></p>`))}e.properties&&e.properties.length>0&&(s+=`<p><em>属性: ${e.properties.join(", ")}</em></p>`),e.magicEffects&&e.magicEffects.length>0&&(s+="<p>魔法效果:<ul>",e.magicEffects.forEach((e=>{s+=`<li class="magic-effect"><em>${e.notes||e.type}:</em> `,"object"==typeof e.value&&null!==e.value?s+=Object.entries(e.value).map((([e,t])=>`${e}: ${t}`)).join(", "):s+=`${e.value}`,e.condition&&(s+=` <span class="effect-condition">(${e.condition})</span>`),s+="</li>"})),s+="</ul></p>"),t+=s||"<p>无更多详情。</p>",t+="</div></li>"})),t+="</ul>",X.innerHTML=t}else X.innerHTML='<ul id="backpack-inventory-list"><li class="placeholder">物品栏为空。</li></ul>'}function Ue(e){if(!e)return;H&&(H.textContent=`角色名: ${e.name||"N/A"}`),j&&(j.textContent=`种族/职业: ${e.race||"N/A"} / ${e.class||"N/A"}`),U&&(U.textContent=`等级: ${e.level||0}`),q&&(q.textContent=`生命值: ${e.hp?.current||"?"}/${e.hp?.max||"?"}`),R&&(R.textContent=`AC: ${function(e){let t=e.ac;return e.equipment.forEach((e=>{e.equipped&&e.magicEffects&&e.magicEffects.forEach((e=>{"AC_BONUS"===e.type&&"number"==typeof e.value&&(t+=e.value)}))})),t}(e)}`),O&&(O.textContent=`时间: ${e.time||"N/A"}`),P&&(P.textContent=`地点: ${e.currentLocation||"N/A"}`);const t=e=>e?`${e.final}(${e.mod>=0?"+":""}${e.mod})`:"N/A";if(Se&&(Se.textContent=`力量: ${t(e.attributes?.strength)}`),Le&&(Le.textContent=`敏捷: ${t(e.attributes?.dexterity)}`),Ie&&(Ie.textContent=`体质: ${t(e.attributes?.constitution)}`),Ce&&(Ce.textContent=`智力: ${t(e.attributes?.intelligence)}`),we&&(we.textContent=`感知: ${t(e.attributes?.wisdom)}`),De&&(De.textContent=`魅力: ${t(e.attributes?.charisma)}`),Ae&&(Ae.textContent=`${e.exp||0}`),Te&&(Te.textContent=`${e.exhaustion||0}`),ke&&(ke.innerHTML=e.proficiencies?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>"),Me&&(Me.innerHTML=e.skills?.filter((e=>e.proficient)).map((e=>`<li>${e.name} (${e.finalValue>=0?"+":""}${e.finalValue})</li>`)).join("")||"<li>无熟练技能</li>"),Be){let t="";if(e.spellSlots&&Object.keys(e.spellSlots).length>0)for(const n in e.spellSlots){const a=e.spellSlots[n];t+=`<div>${n}环: ${a.current}/${a.max}</div>`}Be.innerHTML=t||"无"}qe&&(qe.innerHTML=e.equippedSpells?.map((e=>`<li>${e.name} (${e.level}环)</li>`)).join("")||"<li>无</li>"),Pe&&(Pe.innerHTML=e.activeQuests?.map((e=>`<li>${e}</li>`)).join("")||"<li>无</li>")}function Re(e){if(M){if(M.innerHTML="",e.narrative.forEach((e=>{if(!M)return;const t=document.createElement("p");let n=e.content.replace(/\n/g,"<br>");switch(e.type){case"description":default:t.innerHTML=n;break;case"dialogue":t.innerHTML=`<strong>${e.speaker||"某人"}${e.emotion?` (${e.emotion})`:""}:</strong> ${n}`;break;case"systemMessage":t.className="system-message",t.innerHTML=`<em>${n}</em>`;break;case"actionDescription":t.innerHTML=`<em>${e.actor||"某物"} ${n}</em>`;break;case"thought":t.className="thought-message",t.innerHTML=`<i>"${n}"</i>`}M.appendChild(t)})),e.combatLog&&e.combatLog.length>0){const t=document.createElement("div");t.className="combat-log",t.innerHTML="<h4>战斗记录:</h4>"+e.combatLog.map((e=>`<p>${e.replace(/\n/g,"<br>")}</p>`)).join(""),M.appendChild(t)}if(e.enemies&&e.enemies.length>0){const t=document.createElement("div");t.className="enemies-display";let n="<h4>当前人物:</h4><ul>";e.enemies.forEach((e=>{n+=`<li><strong>${e.name} (ID: ${e.id})</strong> - HP: ${e.hp.current}/${e.hp.max}, AC: ${e.ac}`,e.intent&&(n+=`, 意图: ${e.intent}`),e.statusEffects&&e.statusEffects.length>0&&(n+=`, 状态: ${e.statusEffects.join(", ")}`),n+="</li>"})),n+="</ul>",t.innerHTML=n,M.appendChild(t)}}}function Je(e,t){B&&(B.innerHTML="",e&&e.length>0?e.forEach((e=>{if(!B)return;const n=document.createElement("button");n.id=`choice-${e.id}`,n.textContent=e.text,n.dataset.action=e.actionCommand,n.addEventListener("click",(()=>t(e))),B.appendChild(n)})):B.innerHTML="<p>暂无行动选项。</p>")}function Ge(t){!function(e){if(!ne)return;let t="";if(e.spellSlots&&Object.keys(e.spellSlots).length>0)for(const n in e.spellSlots){const a=e.spellSlots[n];t+=`<div class="spell-slot-level">\n        <span class="spell-slot-label">${"0"===n?"戏法":`${n}环`}:</span>\n        <span class="spell-slot-count">${a.current}/${a.max}</span>\n      </div>`}else t='<div class="no-spell-slots">无法术槽</div>';ne.innerHTML=t}(t),function(t){if(!ae)return;if(!t.equippedSpells)return ae.innerHTML='<li class="placeholder">无已准备法术。</li>',void e("warning","玩家没有equippedSpells数组","Debug - 已准备法术");if(t.equippedSpells.length>0){let e="";t.equippedSpells.forEach((n=>{const a=D(n.name,t);e+=`<li class="${a?"prepared-spell":"prepared-spell disabled"}" data-spell-name="${n.name}">\n        <span class="spell-name">${n.name}</span>\n        <span class="spell-level">${0===n.level?"戏法":`${n.level}环`}</span>\n        ${a?'<button class="cast-prepared-spell-button">施放</button>':'<span class="no-slots">无法术槽</span>'}\n      </li>`})),ae.innerHTML=e}else ae.innerHTML='<li class="placeholder">无已准备法术。</li>'}(t),Ve(t)}function Ve(t){if(!oe)return;if(!t.equippedSpells||0===t.equippedSpells.length)return void(oe.innerHTML='<li class="placeholder">角色没有已学会的法术。</li>');const n=v();if(!n||0===n.length)return oe.innerHTML='<li class="placeholder">法术模板未加载。</li>',void e("warning","法术模板未加载或为空","Debug - 法术列表");let a=t.equippedSpells.map((e=>{const t=n.find((t=>t.name_zh===e.name||t.name_en===e.name));return t?{...t,equippedLevel:e.level}:null})).filter((e=>null!==e));const s=document.getElementById("spell-level-filter");if(s&&"all"!==s.value){const e=parseInt(s.value);a=a.filter((t=>t.level===e))}const i=document.getElementById("spell-school-filter");if(i&&"all"!==i.value&&(a=a.filter((e=>e.school===i.value))),a.length>0){let e="";a.forEach((n=>{if(!n)return;const a=D(n.name_zh,t);e+=`<li class="${a?"available-spell":"available-spell disabled"}" data-spell-name="${n.name_zh}">\n        <div class="spell-header">\n          <span class="spell-name">${n.name_zh}</span>\n          <span class="spell-level">${0===n.level?"戏法":`${n.level}环`}</span>\n          <span class="spell-school">${n.school}</span>\n        </div>\n        <div class="spell-summary">${n.description_short}</div>\n        <div class="spell-actions">\n          <button class="view-spell-button">查看详情</button>\n          ${a?'<button class="quick-cast-spell-button">快速施放</button>':'<span class="no-slots">无法术槽</span>'}\n        </div>\n      </li>`})),oe.innerHTML=e}else oe.innerHTML='<li class="placeholder">无符合条件的法术。</li>'}function ze(t){const n=v();if(!n||0===n.length)return void e("error","法术模板未加载","Debug - 法术详情");const a=n.find((e=>e.name_zh===t||e.name_en===t));if(!a)return void e("error",`找不到法术: ${t}`,"Debug - 法术详情");if(ce&&(ce.textContent=a.name_zh),de&&(de.textContent=0===a.level?"戏法":`${a.level}环`),pe&&(pe.textContent=a.school),ue&&(ue.textContent=a.casting_time),me&&(me.textContent=a.range),fe&&(fe.textContent=a.components.join(", ")),ge&&(ge.textContent=a.duration),ye&&(ye.textContent=a.description_long),$e)if($e.innerHTML="",0===a.level)$e.innerHTML='<option value="0">戏法 (无需法术槽)</option>';else for(let e=a.level;e<=9;e++)$e.innerHTML+=`<option value="${e}">${e}环法术槽</option>`;!function(){if(!he)return;Array.from(he.options).forEach((e=>{"self"!==e.value&&"custom"!==e.value&&he?.removeChild(e)}));const t=c?.enemies||[];t.length>0&&e("info",`场景中有 ${t.length} 个可选目标`,"Debug - 目标选择");const n=he.querySelector('option[value="custom"]');t.forEach((e=>{const t=document.createElement("option");t.value=`npc:${e.id}`,t.textContent=`${e.name} (${e.hp.current}/${e.hp.max} HP)`,n?he?.insertBefore(t,n):he?.appendChild(t)}))}();const s=document.getElementById("spell-detail-modal");s&&(s.style.display="flex")}function Fe(e){if(!e)return M&&(M.innerHTML="<p>错误：无法加载场景数据。</p>"),void(B&&(B.innerHTML=""));if(e.sceneUuid&&l.lastProcessedSceneUuid===e.sceneUuid){console.warn(`[AdvLogV3] Scene ${e.sceneUuid} already processed. Skipping variable updates. Rendering UI only.`),$(e),Re(e),Ue(l),Je(e.playerChoices,We),B&&B.querySelectorAll("button").forEach((e=>e.disabled=!1));const t=M?.querySelector("p > i");return void(t?.parentElement&&M&&M.removeChild(t.parentElement))}$(e);const t=JSON.parse(JSON.stringify(l));t.currentLocation=e.currentLocation,t.time=e.time,e.variableUpdates&&e.variableUpdates.length>0&&e.variableUpdates.forEach((n=>{let a=null;if("玩家"===n.target)a=t;else{const t=n.target;if("string"==typeof t&&t.startsWith("敌人ID:")){const n=t.substring(5);if(e.enemies&&Array.isArray(e.enemies)){const t=e.enemies.find((e=>e.id===n));t?a=t:console.warn(`[applySceneData] Enemy with ID ${n} not found in current scene for variable update.`)}else console.warn(`[applySceneData] scene.enemies is not an array or is undefined. Cannot update enemy ID: ${n}`)}else console.warn(`[applySceneData] Target '${n.target}' is not '玩家' and not a valid enemy ID format.`)}a&&function(e,t,n,a){const s=t.split(".");let i=e;for(let e=0;e<s.length-1;e++){if(void 0===i[s[e]]||"object"!=typeof i[s[e]])return void console.error(`[applyVariableUpdate] Path part ${s[e]} not found or not an object in target.`);i=i[s[e]]}const o=s[s.length-1];switch(n){case"设置":"object"==typeof i&&null!==i&&(i[o]=a);break;case"增加":"object"==typeof i&&null!==i&&"number"==typeof i[o]&&"number"==typeof a&&(i[o]+=a);break;case"减少":"object"==typeof i&&null!==i&&"number"==typeof i[o]&&"number"==typeof a&&(i[o]-=a);break;case"添加元素":"object"==typeof i&&null!==i&&Array.isArray(i[o])&&i[o].push(a);break;case"移除元素":if("object"==typeof i&&null!==i&&Array.isArray(i[o])){const e=i[o].indexOf(a);e>-1&&i[o].splice(e,1)}break;case"物品获得":if("inventory"===t&&Array.isArray(i.inventory)&&"object"==typeof a&&a.name){const e="number"==typeof a.quantity?a.quantity:1,t=i.inventory.find((e=>e.id&&a.id&&e.id===a.id||e.name===a.name));if(t)t.quantity+=e,t.description=a.details||a.description||t.description,a.baseItemName&&(t.baseItemName=a.baseItemName),a.properties&&(t.properties=a.properties),a.magicEffects&&(t.magicEffects=a.magicEffects),a.type&&(t.type=a.type);else{const t={id:a.id||`item_${Date.now()}_${Math.random().toString(36).substring(2,7)}`,name:a.name,quantity:e,description:a.details||a.description||"",baseItemName:a.baseItemName,properties:a.properties||[],magicEffects:a.magicEffects||[]};a.type&&(t.type=a.type),i.inventory.push(t)}}else console.warn('[applyVariableUpdate] "物品获得" 操作失败：value 无效或 inventory 不是数组。',a,i.inventory);break;case"物品失去":if("inventory"===t&&Array.isArray(i.inventory)&&"object"==typeof a&&a.name&&("number"==typeof a.quantity||void 0===a.quantity)){const e="number"==typeof a.quantity?a.quantity:1,t=a.id?i.inventory.findIndex((e=>e.id===a.id)):i.inventory.findIndex((e=>e.name===a.name));t>-1&&(i.inventory[t].quantity-=e,i.inventory[t].quantity<=0&&i.inventory.splice(t,1))}else console.warn('[applyVariableUpdate] "物品失去" 操作失败：value 无效或 inventory 不是数组。',a,i.inventory);break;default:console.warn(`[applyVariableUpdate] Unknown operation: ${n}`)}}(a,n.path,n.operation,n.value)})),e.sceneUuid&&(t.lastProcessedSceneUuid=e.sceneUuid),y(t),Re(e),Ue(l),Je(e.playerChoices,We),G&&"none"!==G.style.display&&(Oe(l),He(l),je(l))}async function We(d){B&&B.querySelectorAll("button").forEach((e=>e.disabled=!0));const p=M;if(p){const e=document.createElement("p");e.innerHTML="<i>正在等待AI响应...</i>",p.appendChild(e)}let u="";c&&(u+=`紧接之前的场景JSON数据是:\n${JSON.stringify(c,null,2)}\n`);let m="你是一名D&D 5e的地下城主(DM)，正在主持一个文字冒险游戏“冒险日志 v2”。\n";m+=`当前玩家状态：\n${JSON.stringify(l,null,2)}\n`,m+=`\n${u}`;let y="";const $=[/\[DC(\d+)\s+([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(?:检定)?\]/i,/\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:检定)?\]/i,/\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:-\s*[^\]]+)?\]/i,/\[DC(\d+)\s+([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,/\[(?:进行)?DC(\d+)(?:的)?([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i];let h,b=null,v=0,E="";for(let t=0;t<$.length;t++){const n=$[t],a=d.text.match(n);if(a){b=a,0===t||3===t||4===t?(v=parseInt(a[1],10),E=a[2].trim(),h=a[3]?a[3].trim():void 0):1!==t&&2!==t||(E=a[1].trim(),h=a[2]?a[2].trim():void 0,v=parseInt(a[3],10));e("info",`检定解析成功: ${["标准格式 [DC15 力量(运动)]","变体格式1 [力量(运动) DC15]","变体格式2 [魅力(威吓) DC14 - 描述]","变体格式3 [DC13 魅力检定(游说)]","变体格式4 [进行DC15的力量检定]"][t]} -> DC${v} ${E}${h?`(${h})`:""}`,"检定解析");break}}let _=d.text.match(/\[攻击\s+(.+?)\s+使用\s+(.+?)\s+DC(\d+)\]/i);if(d.actionCommand?.startsWith("cast_spell_save:")){const[,n,a,s]=d.actionCommand.split(":"),o=parseInt(s)||0,r=w(n);if(r&&r.save&&D(n,l)){const s=8+(l.attributes[l.spellcastingAbility?.toLowerCase()]?.mod||0)+t(l.level),c=Math.max(1,2*r.level),d=Math.random()<.3,p=i(s,r.save.attribute,c,d);r.level>0&&A(l,o);const u=p.success?"豁免成功":"豁免失败";let m=`投骰1d20[${p.roll}]`;p.modifier>0?m+=`+${p.modifier}(${p.attribute})`:p.modifier<0&&(m+=`${p.modifier}(${p.attribute})`),m+=`=${p.total}`,p.isCritical?m+=" [天然20]":p.isFumble&&(m+=" [天然1]"),y=` [${n} 法术 DC${s} ${p.attribute}豁免 ${m} ${u}]`;const f=`${n} 对 ${a}: DC${s} ${p.attribute}豁免 -> ${u} (${m})`;e(p.success?"info":"success",f,"法术豁免结果"),M&&(M.innerHTML+=`<p class="check-result"><em>${f}</em></p>`)}}else if(d.actionCommand?.startsWith("cast_spell_utility:")){const[,t,n]=d.actionCommand.split(":"),a=parseInt(n)||0,s=w(t);s&&D(t,l)&&(s.level>0&&A(l,a),y=` [施放 ${t} 法术成功]`,e("success",`成功施放 ${t}`,"法术施放"),M&&(M.innerHTML+=`<p class="check-result"><em>你施放了 ${t}</em></p>`))}if(_){const i=_[1].trim(),c=_[2].trim(),d=parseInt(_[3],10);let p="strength",u=!1;const m=l.equipment?.find((e=>e.name===c&&e.equipped)),f=l.equippedSpells?.find((e=>e.name===c));let g="normal";if(m&&!u){const t=s(m,r),n=function(e){const t=e.find((e=>e.startsWith("弹药")));if(t){const e=t.match(/；\s*([^)]+)\)/);if(e&&e[1])return e[1].trim()}return null}(t);if(n){const t=l.inventory?.find((e=>e.name===n));if(!t||t.quantity<=0){if(e("error",`无法攻击：缺少弹药 (${n})！`,"弹药不足"),M){const e=document.createElement("p");e.className="system-message",e.innerHTML=`<em>你试图使用 ${m.name}，但发现没有 ${n} 了！</em>`,M.appendChild(e)}B&&B.querySelectorAll("button").forEach((e=>e.disabled=!1));const t=M?.querySelector("p > i");return void(t?.parentElement&&M&&M.removeChild(t.parentElement))}}const a=["半身人","侏儒"];t.includes("重型")&&a.includes(l.race)&&(g="disadvantage",e("info",`你使用重型武器 (${m.name}) 进行攻击，体型过小导致攻击具有劣势。`,"重型武器劣势"))}f?(u=!0,p=l.spellcastingAbility?.toLowerCase()||"intelligence"):m&&s(m,r).includes("灵巧")&&l.attributes.dexterity.mod>l.attributes.strength.mod&&(p="dexterity");const $=o(d,p,u?c:m?m.name:void 0,l,m,g);let h=`投骰1d20[${$.roll}]`;"advantage"===$.advantageState&&(h+="(优势)"),"disadvantage"===$.advantageState&&(h+="(劣势)"),h+=`${$.attributeMod>=0?"+":""}${$.attributeMod}(${$.attributeName})`,0!==$.proficiencyBonusApplied&&(h+=`${$.proficiencyBonusApplied>=0?"+":""}${$.proficiencyBonusApplied}(熟练)`);const b=m?.magicEffects?.find((e=>"ATTACK_BONUS"===e.type))?.value;"number"==typeof b&&0!==b&&(h+=`${b>=0?"+":""}${b}(武器)`),h+=`=${$.total}`;let v=$.isFumble?"自动失手":$.isCritical?"重击":$.success?"攻击命中":"攻击失手",E="";if($.success&&m){const t=p,o=l.attributes[t]?.mod||0,u=function(e,t,i,o,l){let r=e.damage||"",c=e.damageType||"未知";const d=e.name;let p=[],u=!1;if(e.baseItemName&&o.length>0){const t=o.find((t=>t.name_zh===e.baseItemName||t.name_en===e.baseItemName));t&&(r=t.damage,c=t.damageType)}const m=s(e,o);if(l){const e=m.find((e=>e.startsWith("多用")));if(e){const t=e.match(/多用\s*\(([^)]+)\)/);t&&t[1]&&(r=t[1],u=!0)}}let f=0;const g=[],y=n(r);if(y){let e=a(y.count,y.die),n=`${y.count}d${y.die}`,s=u?`基础(多用 ${n})`:`基础(${n})`;if(i?(e+=a(y.count,y.die),p.push(`重击${s}: ${e-y.modifier}`)):p.push(`${s}: ${e-y.modifier}`),e+=y.modifier,f+=e,g.push({type:c,amount:e,source:d}),0!==t){f+=t,p.push(`属性: ${t}`);const e=g.find((e=>e.type===c&&e.source===d));e&&(e.amount+=t)}}else p.push("无基础伤害骰");e.magicEffects?.forEach((e=>{if("DAMAGE_BONUS_STATIC"===e.type&&"number"==typeof e.value?.amount){const t=e.value.amount,n=e.value.type||c;f+=t,p.push(`固定(${n}): ${t}`);let a=g.find((e=>e.type===n&&"魔法效果"===e.source));a?a.amount+=t:g.push({type:n,amount:t,source:"魔法效果"})}})),e.magicEffects?.forEach((e=>{if("DAMAGE_BONUS_DICE"===e.type&&"string"==typeof e.value?.dice&&"string"==typeof e.value?.type){const t=n(e.value.dice);if(t){let n=t.count,s=`${n}d${t.die}`;i&&(n*=2,s=`${t.count}d${t.die}x2`);let o=a(n,t.die);o+=t.modifier,f+=o,p.push(`附加(${e.value.type} ${s}): ${o}`);let l=g.find((t=>t.type===e.value.type&&"魔法效果"===t.source));l?l.amount+=o:g.push({type:e.value.type,amount:o,source:"魔法效果"})}}}));const $=p.join(" + ")+` = ${Math.max(0,f)}`,h=[...new Set(g.map((e=>e.type)))].join("/");return{totalDamage:Math.max(0,f),damageType:h||"未知",details:$,breakdown:g}}(m,o,$.isCritical||!1,r);E=` 造成 ${u.totalDamage} 点 ${u.damageType} 伤害 (${u.details})。`;const f=`${c} 攻击 ${i}: ${$.total} vs DC${d} -> ${v} (${h}).${E}`;e($.success?"success":"error",f,"攻击与伤害结果"),M&&(M.innerHTML+=`<p class="check-result"><em>${f}</em></p>`)}else if($.success&&f){const s=w(c);if(s&&D(c,l)){const o=function(e,s,i=!1,o){const l=w(e);if(!l)throw new Error(`未找到法术模板: ${e}`);if(!l.damage)return{totalDamage:0,damageType:"无伤害",details:"此法术不造成伤害",breakdown:[]};let r=0,c=[],d=[],p=l.num_projectiles||1;const u=s.spellcastingAbility?.toLowerCase()||"intelligence",m=s.attributes[u]?.mod||0;let f=l.damage;if(0===l.level&&l.scaling){const e=s.level;for(const[t,n]of Object.entries(l.scaling))e>=parseInt(t)&&(f=n)}if(o&&o>l.level&&l.higher_level_cast){const e=o-l.level,t=l.higher_level_cast.effect;if(t.includes("增加")&&t.includes("d")){const s=t.match(/增加(\d+d\d+)/);if(s){const t=s[1],o=n(t);if(o)for(let n=0;n<e;n++){let e=a(o.count,o.die);i&&(e+=a(o.count,o.die)),r+=e,d.push(`升环+${n+1}(${t}): ${e}`)}}}else(t.includes("飞弹")||t.includes("投射物"))&&(p+=e)}const g=n(f);if(g){for(let e=0;e<p;e++){let t=a(g.count,g.die);i&&(t+=a(g.count,g.die)),t+=g.modifier,l.add_casting_modifier_to_damage&&(t+=m),r+=t,p>1?d.push(`投射物${e+1}: ${t}`):d.push(`基础伤害: ${t}`)}c.push({type:l.damage_type||"魔法",amount:r,source:e})}const y=d.join(" + ")+` = ${r}`;let $;if(l.save){const e=8+m+t(s.level);$={attribute:l.save.attribute,dc:e,effectOnSuccess:l.save.effect_on_success}}return{totalDamage:r,damageType:l.damage_type||"魔法",details:y,breakdown:c,projectileCount:p>1?p:void 0,saveRequired:$}}(c,l,$.isCritical||!1);o.totalDamage>0&&(E=` 造成 ${o.totalDamage} 点 ${o.damageType} 伤害 (${o.details})。`),s.level>0&&(A(l,s.level),E+=` 消耗了 ${s.level} 环法术位。`);const r=`${c} 攻击 ${i}: ${$.total} vs DC${d} -> ${v} (${h}).${E}`;e($.success?"success":"error",r,"法术攻击与伤害结果"),M&&(M.innerHTML+=`<p class="check-result"><em>${r}</em></p>`)}else{const t=`${c} 攻击 ${i}: ${$.total} vs DC${d} -> ${v} (${h}) - 法术槽不足或法术不存在！`;e("error",t,"法术攻击失败"),M&&(M.innerHTML+=`<p class="check-result"><em>${t}</em></p>`)}}else{const t=`${c} 攻击 ${i}: ${$.total} vs DC${d} -> ${v} (${h})`;e($.success?"success":"error",t,"攻击结果"),M&&(M.innerHTML+=`<p class="check-result"><em>${t}</em></p>`)}let S="";$.success&&m&&m.magicEffects&&m.magicEffects.forEach((e=>{if("ON_HIT_EFFECT_SAVE"===e.type&&e.value){const t=e.value.saveAttribute,n=e.value.dc,a=e.value.effectOnFail,s=e.notes||"额外效果";t&&"number"==typeof n&&(S+=` 目标 (${i}) 还需要进行一次 DC${n} 的 ${t} 豁免检定以抵抗 ${m.name} 的 ${a||s}。`)}})),y=` [${v} DC${d} ${p}(${c}) ${h}]${E}${S}`}else if(b&&v>0)if(E.toLowerCase().includes("先攻")){const t=o(0,"敏捷",void 0,l);let n=`投骰1d20[${t.roll}]${t.attributeMod>=0?"+":""}${t.attributeMod}(敏捷)=${t.total}`;y=` [先攻检定 ${n}]`,e("info",`先攻: ${t.total} (${n})`,"先攻"),M&&(M.innerHTML+=`<p class="check-result"><em>先攻: ${t.total} (${n})</em></p>`)}else{const t=o(v,E,h,l);let n=`投骰1d20[${t.roll}]`;0!==t.attributeMod&&(n+=`${t.attributeMod>=0?"+":""}${t.attributeMod}(${t.attributeName})`),0!==t.proficiencyBonusApplied&&(n+=`${t.proficiencyBonusApplied>=0?"+":""}${t.proficiencyBonusApplied}(熟练)`),n+=`=${t.total}`;let a=t.isFumble?"自动失败":t.isCritical?"大成功":t.success?"检定成功":"检定失败";y=` [${a} DC${v} ${t.attributeName}${t.skillName?`(${t.skillName})`:""} ${n}]`;const s=`${t.attributeName}${t.skillName?`(${t.skillName})`:""} 检定: ${t.total} vs DC${v} -> ${a} (${n})`;e(t.success?"success":"error",s,"检定结果"),M&&(M.innerHTML+=`<p class="check-result"><em>${s}</em></p>`)}if(m+=`\n玩家刚刚选择了行动： "${d.text}"${y}\n`,m+=`\n请根据玩家的选择${y?"和检定结果（包括伤害、是否触发后续效果如目标豁免等）":""}，继续发展剧情。`,m+='\n\n【重要格式提醒】你的回复必须是包裹在 "查看系统\\n##@@_ADVENTURE_BEGIN_@@##" 和 "##@@_ADVENTURE_END_@@##\\n关闭系统" 内的单一JSON对象。JSON结构需严格遵循AdventureSceneJSON Schema。JSON内部绝对不允许包含任何注释 (如 // 或 /* */)。',m+='\n\n【检定格式重要提醒】在playerChoices的text字段中嵌入检定信息时，请严格使用标准格式：[DC{数值} {属性}({技能})] 或 [DC{数值} {属性}]。示例："尝试撬锁[DC15 敏捷(巧手)]"、"说服守卫[DC12 魅力(游说)]"。虽然客户端支持格式变体，但标准格式能确保最佳的解析稳定性。',m+="\n\n请特别注意处理由客户端反馈的任何需要目标进行豁免检定的情况，并在你的narrative中描述豁免过程和结果，同时通过variableUpdates更新目标状态。详细规则请参考总纲提示词。","function"==typeof triggerSlash)try{const t=await triggerSlash(`/gen ${m}`),n=M,a=n?.querySelector("p > i");if(a?.parentElement&&n&&n.removeChild(a.parentElement),t?.trim()){let a=null;const s=t.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);s&&s[1]?a=s[1].trim():console.warn("Unique markers ##@@_ADVENTURE_BEGIN_@@##...##@@_ADVENTURE_END_@@## not found in AI response.");let i=null;if(a&&(i=g(a)),i){Fe(i),await f();const n="dndRPG_history.json";let a=`scene_${Date.now()}_${Math.random().toString(36).substring(2,8)}`;if(i.sceneTitle&&i.time){const e=e=>e.replace(/[\\/:*?"<>|]/g,"_").replace(/\s+/g,"_"),t=e(i.sceneTitle).substring(0,40),n=e(i.time).substring(0,25);a=`${t}_${n}_${Math.random().toString(36).substring(2,7)}`}let s=t;const o=/查看系统\s*##@@_ADVENTURE_BEGIN_@@##[\s\S]*?##@@_ADVENTURE_END_@@##\s*关闭系统/,l=t.match(o);l&&l[0]?s=l[0]:(e("warning","未能从AI回复中提取标准的干净记录块，将保存完整原始回复。","历史记录警告"),console.warn("[AdvLogV2 History] Could not extract clean block, saving raw response. Raw:",t));try{const t=`/createentry file="${n}" key="${a}" ${s}`,i=await triggerSlash(t);if(i&&""!==i.trim()){e("info",`历史场景已保存到 ${n} (Key: ${a}, UID: ${i})`,"历史记录");const t=`/setentryfield file="${n}" uid="${i.trim()}" field=constant true`;await triggerSlash(t),e("success",`历史条目 ${a} 已激活 (蓝灯).`,"历史记录")}else e("warning",`保存历史场景 ${a} 到 ${n} 未返回有效UID。`,"历史记录警告")}catch(t){e("error",`保存或激活历史场景 ${a} 到 ${n} 失败: ${t.message}`,"历史记录错误"),console.error(`Error saving/activating history entry ${a}:`,t)}}else e("error","JSON提取或解析失败，请查看主区域显示的AI原始回复。","处理错误"),n&&(n.innerHTML+=`<p style="color: lightcoral; font-family: monospace; white-space: pre-wrap; border: 1px solid orange; padding: 10px; background-color: #333;"><strong>[调试] AI 原始回复 (提取或解析失败):</strong>\n${t.replace(/</g,"<").replace(/>/g,">")}</p>`),console.error("Failed to extract or parse JSON. Raw AI Response:",t),a||!s||s[1]?a?console.error("Reason: JSON.parse failed. Extracted string was:",a):console.error("Reason: Core JSON string could not be extracted (markers not found)."):console.error("Reason: Markers found, but no content between them or other regex issue.")}else n&&(n.innerHTML+="<p style='color:orange;'>AI未返回有效数据。</p>"),e("warning","AI未返回任何数据。","无响应")}catch(t){const n=M,a=n?.querySelector("p > i");a?.parentElement&&n&&n.removeChild(a.parentElement),n&&(n.innerHTML+=`<p style='color:red;'>与AI交互时发生错误: ${t.message}</p>`),e("error",`与AI交互时发生错误: ${t.message}`,"交互错误")}finally{B&&B.querySelectorAll("button").forEach((e=>e.disabled=!1))}else{B&&B.querySelectorAll("button").forEach((e=>e.disabled=!1));const e=M?.querySelector("p > i");e?.parentElement&&M&&M.removeChild(e.parentElement)}}async function Ye(){if(!N||!x||!T)return;N.disabled=!0,N.textContent="正在加载角色...";const t=await async function(e){if("function"!=typeof triggerSlash)return null;const t="RPG_Modules_Test.json";try{const n=`/findentry file="${t}" "${e}"`,a=await triggerSlash(n);if(!a?.trim()||"[]"===a.trim())return null;let s=null;try{const e=JSON.parse(a);Array.isArray(e)&&e.length>0?s=e[0].toString():"string"==typeof e&&""!==e.trim()?s=e.trim():"number"==typeof e&&(s=e.toString())}catch(e){"string"==typeof a&&""!==a.trim()&&(s=a.trim())}if(!s)return null;const i=`/getentryfield file="${t}" field=content "${s}"`,o=await triggerSlash(i);return o?.trim()?o:null}catch(e){return null}}("PLAYER");let n=m();if(t)try{const e=JSON.parse(t);e?.name&&e.attributes&&(n=e)}catch(t){e("warning","角色数据解析失败，使用默认角色。","新游戏")}y(n),$(null);const a={sceneType:"location",sceneTitle:"冒险的序章",currentLocation:(s=l).currentLocation||"未知起点",time:s.time||"某个时刻",narrative:[{type:"description",content:"你已准备好踏上征程！请选择你的第一个行动，让传奇开始！"}],playerChoices:[{id:"A",text:"根据我的模组设定，正式开始冒险！",actionCommand:"start_adventure_module"},{id:"B",text:"我应该先了解一下我所处的环境（基于模组）。",actionCommand:"survey_environment_module"},{id:"C",text:"查看我的角色状态。",actionCommand:"check_character_status"}]};var s;if(Fe(a),"function"==typeof getLastMessageId&&"function"==typeof triggerSlash&&"function"==typeof setChatMessages){if(void 0!==getLastMessageId()){await f();const t="dndRPG_history.json",n=`scene_init_${Date.now()}`,s=`查看系统\n##@@_ADVENTURE_BEGIN_@@##\n${JSON.stringify(a)}\n##@@_ADVENTURE_END_@@##\n关闭系统`;try{const e=`/createentry file="${t}" key="${n}" ${s}`,a=await triggerSlash(e);if(a&&""!==a.trim()){const e=`/setentryfield file="${t}" uid="${a.trim()}" field=constant true`;await triggerSlash(e)}}catch(t){e("error",`保存初始历史场景失败: ${t.message}`,"新游戏错误")}await f()}}Ue(l),T&&(T.style.display="none"),x&&(x.style.display="flex"),t?!N||l?.name&&l.attributes||(N.disabled=!1,N.textContent="开始新冒险 (从世界书加载角色)"):"function"!=typeof getLastMessageId&&N&&(N.disabled=!1,N.textContent="开始新冒险 (从世界书加载角色)")}function Ke(){const t=document.getElementById("spell-detail-name"),n=document.getElementById("spell-slot-level");if(!t||!n)return void e("error","法术施放界面元素缺失","错误");const a=t.textContent||"",s=function(){if(!he)return"";const t=he.value;if("self"===t)return"自己";if("custom"===t){const t=be?.value.trim()||"";return t&&e("info",`自定义目标: ${t}`,"Debug - 目标选择"),t}if(t.startsWith("npc:")){const e=t.substring(4),n=c?.enemies?.find((t=>t.id===e));return n?.name||""}return""}(),i=parseInt(n.value)||0;e("info",`施放 ${a} → ${s||"无目标"} (${i}环)`,"Debug - 法术施放"),a?D(a,l)?(le&&(le.style.display="none"),Qe(a,s,i)):e("error","无法施放此法术：法术槽不足","法术施放失败"):e("error","未选择法术","错误")}function Qe(t,n,a){const s=w(t);if(!s)return void e("error",`找不到法术模板: ${t}`,"错误");const i=c?.enemies||[],o=i.length>0;let l="",r="";const d=s.attack_type||s.save;if(d&&o&&n)if(s.attack_type){l=`[攻击 ${n} 使用 ${t} DC${15}]`,r=`attack:${t}:${n}`}else s.save&&(l=`对 ${n} 施放 ${t} [${s.save.attribute}豁免]`,r=`cast_spell_save:${t}:${n}:${a}`);else if(d&&o&&!n){l=`施放 ${t} (场景中有: ${i.map((e=>e.name)).join("、")}，请选择目标)`,r=`cast_spell_choose_target:${t}:${a}`}else d&&o?(l=`施放 ${t}`,r=`cast_spell:${t}:${n||"无目标"}:${a}`):(l=o?`施放 ${t} (无需目标)`:`施放 ${t} (场景中无目标)`,r=`cast_spell_utility:${t}:${a}`);e("success",`发送法术动作: ${l}`,"Debug - 发送AI"),We({id:"spell_cast",text:l,actionCommand:r})}async function Xe(){setTimeout((async()=>{if(function(){try{"undefined"!=typeof window&&"undefined"!=typeof parent&&parent!==window&&["$","toastr","triggerSlash","getLastMessageId","setChatMessages"].forEach((e=>{void 0===window[e]&&void 0!==parent[e]&&(window[e]=parent[e])}))}catch(e){}}(),T=document.getElementById("start-screen-container"),N=document.getElementById("start-new-game-button"),x=document.getElementById("adventure-log-container"),k=document.getElementById("player-status-area"),M=document.getElementById("main-narrative-area"),B=document.getElementById("action-choices-area"),q=document.getElementById("health"),P=document.getElementById("location"),O=document.getElementById("time"),H=document.getElementById("char-name-display"),j=document.getElementById("char-race-class-display"),U=document.getElementById("char-level-display"),R=document.getElementById("ac-display"),Se=document.getElementById("attr-str-display"),Le=document.getElementById("attr-dex-display"),Ie=document.getElementById("attr-con-display"),Ce=document.getElementById("attr-int-display"),we=document.getElementById("attr-wis-display"),De=document.getElementById("attr-cha-display"),Ae=document.getElementById("exp-display"),Te=document.getElementById("exhaustion-display"),Ne=document.getElementById("toggle-char-sheet-button"),xe=document.getElementById("detailed-character-sheet"),J=document.getElementById("toggle-backpack-button"),G=document.getElementById("backpack-interface"),V=document.getElementById("close-backpack-button"),z=document.getElementById("backpack-currency-gold"),F=document.getElementById("backpack-currency-silver"),W=document.getElementById("backpack-currency-copper"),K=document.getElementById("backpack-equipped-items-area"),Y=document.getElementById("backpack-equipped-list"),X=document.getElementById("backpack-inventory-items-area"),Q=document.getElementById("backpack-inventory-list"),Z=document.getElementById("toggle-spellbook-button"),ee=document.getElementById("spellbook-interface"),te=document.getElementById("close-spellbook-button"),ne=document.getElementById("spellbook-spell-slots-display"),ae=document.getElementById("spellbook-prepared-spells-list"),se=document.getElementById("spell-level-filter"),ie=document.getElementById("spell-school-filter"),oe=document.getElementById("spellbook-available-spells-list"),le=document.getElementById("spell-detail-modal"),re=document.getElementById("close-spell-detail-button"),ce=document.getElementById("spell-detail-name"),de=document.getElementById("spell-detail-level"),pe=document.getElementById("spell-detail-school"),ue=document.getElementById("spell-detail-casting-time"),me=document.getElementById("spell-detail-range"),fe=document.getElementById("spell-detail-components"),ge=document.getElementById("spell-detail-duration"),ye=document.getElementById("spell-detail-description"),$e=document.getElementById("spell-slot-level"),he=document.getElementById("spell-target-type"),be=document.getElementById("spell-target-custom"),ve=document.getElementById("custom-target-input"),Ee=document.getElementById("cast-spell-button"),_e=document.getElementById("cancel-spell-button"),ke=document.getElementById("proficiencies-display"),Me=document.getElementById("skills-display"),Be=document.getElementById("spell-slots-display"),qe=document.getElementById("equipped-spells-display"),Pe=document.getElementById("active-quests-display"),await async function(){try{const t=JSON.parse('\n[\n  { "name_zh": "匕首", "name_en": "Dagger", "category": "简易近战", "damage": "1d4", "damageType": "穿刺", "properties": ["灵巧", "轻型", "投掷 (射程 20/60)"], "weight": "1磅", "cost": "2 GP", "mastery": "迅击" },\n  { "name_zh": "短棒", "name_en": "Club", "category": "简易近战", "damage": "1d4", "damageType": "钝击", "properties": ["轻型"], "weight": "2磅", "cost": "1 SP", "mastery": "缓速" },\n  { "name_zh": "手斧", "name_en": "Handaxe", "category": "简易近战", "damage": "1d6", "damageType": "挥砍", "properties": ["轻型", "投掷 (射程 20/60)"], "weight": "2磅", "cost": "5 GP", "mastery": "侵扰" },\n  { "name_zh": "标枪", "name_en": "Javelin", "category": "简易近战", "damage": "1d6", "damageType": "穿刺", "properties": ["投掷 (射程 30/120)"], "weight": "2磅", "cost": "5 SP", "mastery": "缓速" },\n  { "name_zh": "轻锤", "name_en": "Light Hammer", "category": "简易近战", "damage": "1d4", "damageType": "钝击", "properties": ["轻型", "投掷 (射程 20/60)"], "weight": "2磅", "cost": "2 GP", "mastery": "迅击" },\n  { "name_zh": "硬头锤", "name_en": "Mace", "category": "简易近战", "damage": "1d6", "damageType": "钝击", "properties": [], "weight": "4磅", "cost": "5 GP", "mastery": "削弱" },\n  { "name_zh": "长棍", "name_en": "Quarterstaff", "category": "简易近战", "damage": "1d6", "damageType": "钝击", "properties": ["多用 (1d8)"], "weight": "4磅", "cost": "2 SP", "mastery": "失衡" },\n  { "name_zh": "矛", "name_en": "Spear", "category": "简易近战", "damage": "1d6", "damageType": "穿刺", "properties": ["投掷 (射程 20/60)", "多用 (1d8)"], "weight": "3磅", "cost": "1 GP", "mastery": "削弱" },\n  { "name_zh": "轻弩", "name_en": "Light Crossbow", "category": "简易远程", "damage": "1d8", "damageType": "穿刺", "properties": ["弹药 (射程 80/320；弩矢)", "装填", "双手"], "weight": "5磅", "cost": "25 GP", "mastery": "缓速" },\n  { "name_zh": "短弓", "name_en": "Shortbow", "category": "简易远程", "damage": "1d6", "damageType": "穿刺", "properties": ["弹药 (射程 80/320；箭矢)", "双手"], "weight": "2磅", "cost": "25 GP", "mastery": "侵扰" },\n  { "name_zh": "投石索", "name_en": "Sling", "category": "简易远程", "damage": "1d4", "damageType": "钝击", "properties": ["弹药 (射程 30/120；弹丸)"], "weight": "—", "cost": "1 SP", "mastery": "缓速" },\n  { "name_zh": "战斧", "name_en": "Battleaxe", "category": "军用近战", "damage": "1d8", "damageType": "挥砍", "properties": ["多用 (1d10)"], "weight": "4磅", "cost": "10 GP", "mastery": "失衡" },\n  { "name_zh": "长剑", "name_en": "Longsword", "category": "军用近战", "damage": "1d8", "damageType": "挥砍", "properties": ["多用 (1d10)"], "weight": "3磅", "cost": "15 GP", "mastery": "削弱" },\n  { "name_zh": "巨剑", "name_en": "Greatsword", "category": "军用近战", "damage": "2d6", "damageType": "挥砍", "properties": ["重型", "双手"], "weight": "6磅", "cost": "50 GP", "mastery": "擦掠" },\n  { "name_zh": "刺剑", "name_en": "Rapier", "category": "军用近战", "damage": "1d8", "damageType": "穿刺", "properties": ["灵巧"], "weight": "2磅", "cost": "25 GP", "mastery": "侵扰" },\n  { "name_zh": "短剑", "name_en": "Shortsword", "category": "军用近战", "damage": "1d6", "damageType": "穿刺", "properties": ["灵巧", "轻型"], "weight": "2磅", "cost": "10 GP", "mastery": "侵扰" },\n  { "name_zh": "长弓", "name_en": "Longbow", "category": "军用远程", "damage": "1d8", "damageType": "穿刺", "properties": ["弹药 (射程 150/600；箭矢)", "重型", "双手"], "weight": "2磅", "cost": "50 GP", "mastery": "缓速" }\n]\n  ');Array.isArray(t)?r=t:e("error","武器模板数据不是一个有效的JSON数组。","模板加载错误")}catch(t){e("error",`解析内联武器模板JSON失败: ${t.message}`,"模板加载错误")}}(),await I(),!(T&&N&&x&&k&&M&&B&&Ne&&xe&&J&&G&&V&&Z&&ee&&te&&le&&re&&Ee&&_e))return void console.error("One or more critical UI elements are missing from the DOM.");Ne.addEventListener("click",(()=>{if(xe&&Ne){const e="none"===xe.style.display;xe.style.display=e?"block":"none",Ne.textContent=e?"隐藏详细角色卡":"显示详细角色卡"}})),J.addEventListener("click",(()=>{if(G&&J){const e="none"===G.style.display;G.style.display=e?"flex":"none",e?(Oe(l),He(l),je(l),J.textContent="关闭背包"):J.textContent="打开背包"}})),V.addEventListener("click",(()=>{G&&J&&(G.style.display="none",J.textContent="打开背包")})),Z.addEventListener("click",(()=>{if(ee&&Z){const e="none"===ee.style.display;ee.style.display=e?"flex":"none",e?(Ge(l),Z.textContent="关闭法术书"):Z.textContent="打开法术书"}else e("error","法术书界面元素未找到","Debug - 法术书按钮")})),te.addEventListener("click",(()=>{ee&&Z&&(ee.style.display="none",Z.textContent="打开法术书")})),re.addEventListener("click",(()=>{le&&(le.style.display="none")})),_e.addEventListener("click",(()=>{le&&(le.style.display="none")})),Ee.addEventListener("click",(()=>{Ke()})),he&&he.addEventListener("change",(()=>{!function(){if(!he||!ve)return;const e=he.value;ve.style.display="custom"===e?"block":"none"}()})),N.addEventListener("click",Ye);const t=await async function(){if("function"!=typeof getLastMessageId||"function"!=typeof triggerSlash)return e("error","loadGameState: SillyTavern API functions not found.","Load Debug"),{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};const t=getLastMessageId();if(d=void 0===t?null:t,null===d)return e("warning","loadGameState: currentHostMessageId is null. Cannot load.","Load Debug"),{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let n;try{n=await triggerSlash(`/messages ${d}`)}catch(t){return e("error",`loadGameState: Error fetching host message: ${t.message}`,"Load Debug"),{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1}}if(!n?.trim())return e("warning","loadGameState: rawPersistedState is empty or whitespace.","Load Debug"),{playerStateLoadedFromMsg:!1,sceneDataLoadedFromMsg:!1};let a=!1,s=!1;c=null;const i=new RegExp(`${p}\\s*([\\s\\S]*?)\\s*${u}`),o=n.match(i);if(o?.[1]){const t=o[1].trim();try{const n=JSON.parse(t);n?.name&&n.attributes?(l=n,a=!0):e("warning","loadGameState: Parsed PlayerState lacks essential fields (name/attributes).","Load Debug")}catch(n){e("error",`loadGameState: Error parsing PlayerState JSON: ${n.message}`,"Load Debug"),console.error("[AdvLogV3 Load] Error parsing PlayerState JSON:",n,t)}}const r=n.match(/##@@_ADVENTURE_BEGIN_@@##([\s\S]*?)##@@_ADVENTURE_END_@@##/);if(r&&r[1]){const t=r[1].trim();if(t){const n=g(t);n?(c=n,s=!0):e("warning","loadGameState: parseAIResponse returned null for SceneData.","Load Debug")}else e("warning","loadGameState: SceneData JSON string is empty after trim.","Load Debug")}return{playerStateLoadedFromMsg:a,sceneDataLoadedFromMsg:s}}();t.playerStateLoadedFromMsg&&t.sceneDataLoadedFromMsg&&c?(Fe(c),T&&(T.style.display="none"),x&&(x.style.display="flex")):(T&&(T.style.display="flex"),x&&(x.style.display="none"),Ue(l)),G&&G.addEventListener("click",(async t=>{const n=t.target,a=n.dataset.itemId;if(a){if(n.classList.contains("equip-button"))(function(t){const n=l.inventory.findIndex((e=>e.id===t));if(-1===n)return e("warning",`物品 ${t} 在物品栏中未找到。`,"装备失败"),!1;const a={...l.inventory[n]};if(!a.type||["消耗品","杂物","材料"].includes(a.type))return e("info",`物品 ${a.name} (${a.type}) 不是可装备类型。`,"操作提示"),!1;const s=e=>"盔甲"===e?"armor":"武器"===e?"weapon":e,i=s(a.type),o=[...l.equipment];for(const t of o)if(t.id&&s(t.type)===i){if(!h(t.id))return console.error(`[AdvLog] Failed to unequip ${t.name} while trying to equip ${a.name}`),!1;e("info",`已卸下 ${t.name} 以装备 ${a.name}。`,"自动卸下")}const r=l.inventory.findIndex((e=>e.id===t));-1===r?console.warn(`[AdvLog] Item ${t} no longer in inventory after attempting to unequip others. Proceeding with original itemToEquip data.`):l.inventory[r].quantity>1?l.inventory[r].quantity-=1:l.inventory.splice(r,1);const c={id:a.id,name:a.name,type:a.type||"未知类型",details:a.description,baseItemName:a.baseItemName,properties:a.properties,magicEffects:a.magicEffects,equipped:!0};return a.type||console.warn(`[AdvLog] Equipping item '${a.name}' which was missing a 'type' in inventory. Defaulted to '未知类型'.`),l.equipment.push(c),e("success",`${a.name} 已装备。`,"装备成功"),!0})(a)&&(await f(),He(l),je(l),Ue(l));else if(n.classList.contains("unequip-button"))h(a)&&(await f(),He(l),je(l),Ue(l));else if(n.classList.contains("item-name-toggle")||n.parentElement?.classList.contains("item-name-toggle")){const e=n.classList.contains("item-name-toggle")?n:n.parentElement,t=e?.dataset.itemId;if(t){const n="backpack-equipped-list"===e?.closest("ul")?.id?`details-equipped-${t}`:`details-inventory-${t}`,a=document.getElementById(n);a&&(a.style.display="none"===a.style.display?"block":"none")}}}else if(n.classList.contains("item-name-toggle")){const e=n.closest("li[data-item-id]");if(e){const t=e.dataset.itemId;if(t){const n=e.closest("#backpack-equipped-items-area")?`details-equipped-${t}`:`details-inventory-${t}`,a=document.getElementById(n);a&&(a.style.display="none"===a.style.display?"block":"none")}}}})),ee&&(ee.addEventListener("click",(async t=>{const n=t.target,a=n.dataset.spellName||n.closest("[data-spell-name]")?.getAttribute("data-spell-name");if(n.classList.contains("view-spell-button")&&a)ze(a);else if(n.classList.contains("quick-cast-spell-button")&&a)if(e("info",`快速施放: ${a}`,"Debug - 快速施放"),D(a,l)){const t=w(a);if(!t)return void e("error",`找不到法术模板: ${a}`,"错误");const n=t.level,s=c?.enemies||[],i=s.length>0;i&&e("info",`场景中有 ${s.length} 个可选目标`,"Debug - 快速施放");let o="";const l=["自己"];i&&s.forEach((e=>{l.push(`${e.name} (${e.hp.current}/${e.hp.max} HP)`)})),l.push("自定义目标");const r=l.map(((e,t)=>`${t+1}. ${e}`)).join("\n"),d=prompt(`选择目标:\n${r}\n\n请输入数字 (1-${l.length}) 或直接输入目标名称:`);if(d){const e=parseInt(d);if(e>=1&&e<=l.length)if(1===e)o="自己";else if(e<=l.length-1){const t=e-2;o=s[t]?.name||""}else o=prompt("请输入自定义目标名称:")||"";else o=d}o&&e("info",`选择目标: ${o}`,"Debug - 快速施放"),Qe(a,o,n)}else e("error","无法施放此法术：法术槽不足","法术施放失败");else n.classList.contains("cast-prepared-spell-button")&&a&&ze(a)})),se&&se.addEventListener("change",(()=>{Ve(l)})),ie&&ie.addEventListener("change",(()=>{Ve(l)})))}),200)}window.testCheckParsing=function(){const e=[/\[DC(\d+)\s+([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s*(?:检定)?\]/i,/\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:检定)?\]/i,/\[([^\(\]\s]+)(?:\s*\(([^\)]+)\))?\s+DC(\d+)\s*(?:-\s*[^\]]+)?\]/i,/\[DC(\d+)\s+([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i,/\[(?:进行)?DC(\d+)(?:的)?([^\(\]\s]+)(?:检定)?(?:\s*\(([^\)]+)\))?\]/i],t=["标准格式 [DC15 力量(运动)]","变体格式1 [力量(运动) DC15]","变体格式2 [魅力(威吓) DC14 - 描述]","变体格式3 [DC13 魅力检定(游说)]","变体格式4 [进行DC15的力量检定]"];console.log("=== 检定解析测试开始 ==="),['"尝试撬开箱子[DC15 力量(运动)]"','"说服守卫[DC12 魅力(游说)]"','"感知陷阱[DC14 感知(察觉)]"','"坎尼斯，我们共同的敌人是地精和脑子里的幼体。带我们去德鲁伊林地，对我们双方都有好处。[魅力(游说) DC13]"','"威胁他交出钥匙[魅力(威吓) DC14]"','"软硬兼施地说服他[魅力(威吓) DC14 - 软硬兼施]"','"巧妙地撬锁[敏捷(巧手) DC16 - 小心操作]"','"检查门锁[DC13 敏捷检定(巧手)]"','"观察环境[DC12 感知检定(察觉)]"','"仔细搜索[进行DC15的感知检定]"','"尝试跳跃[进行DC12的力量检定]"'].forEach(((n,a)=>{console.log(`\n测试案例 ${a+1}: ${n}`);let s=!1;for(let a=0;a<e.length;a++){const i=e[a],o=n.match(i);if(o){let e,n=0,i="";0===a||3===a||4===a?(n=parseInt(o[1],10),i=o[2].trim(),e=o[3]?o[3].trim():void 0):1!==a&&2!==a||(i=o[1].trim(),e=o[2]?o[2].trim():void 0,n=parseInt(o[3],10)),console.log(`  ✅ 匹配成功: ${t[a]}`),console.log(`     解析结果: DC${n} ${i}${e?`(${e})`:""}`),s=!0;break}}s||console.log("  ❌ 未匹配到任何格式")})),console.log("\n=== 检定解析测试结束 ===")},window.testSpellSaveLogic=function(){e("info","开始测试法术豁免计算逻辑","豁免测试"),[{dc:10,roll:10,modifier:0,expected:!0},{dc:10,roll:9,modifier:0,expected:!1},{dc:15,roll:12,modifier:3,expected:!0},{dc:15,roll:12,modifier:2,expected:!1},{dc:20,roll:20,modifier:0,expected:!0},{dc:5,roll:1,modifier:10,expected:!0}].forEach(((t,n)=>{const a=t.roll+t.modifier,s=a>=t.dc,i=s?"豁免成功":"豁免失败",o=t.expected?"豁免成功":"豁免失败",l=s===t.expected?"✅":"❌",r=`测试${n+1}: DC${t.dc} 投骰${t.roll}+${t.modifier}=${a} -> ${i} (期望: ${o}) ${l}`;e(s===t.expected?"success":"error",r,"豁免测试")})),e("info","测试新的豁免计算函数","豁免测试");for(let t=0;t<5;t++){const t=i(15,"体质",3,!1),n=`豁免测试: DC${t.dc} ${t.attribute} 投骰${t.roll}+${t.modifier}=${t.total} -> ${t.success?"成功":"失败"}`;e(t.success?"success":"info",n,"新豁免函数测试")}},window.quickSaveTest=function(){e("info","开始快速豁免测试","快速测试"),[{dc:10,expectedSuccess:!0,description:"DC10 应该有合理的成功率"},{dc:20,expectedSuccess:!1,description:"DC20 应该很难成功"},{dc:5,expectedSuccess:!0,description:"DC5 应该很容易成功"}].forEach(((t,n)=>{let a=0;for(let e=0;e<10;e++){i(t.dc,"体质",1,!1).success&&a++}const s=(a/10*100).toFixed(0),o=`测试${n+1}: ${t.description} - 成功率: ${s}% (${a}/10)`;e(5===t.dc&&a>=7||10===t.dc&&a>=3&&a<=8||20===t.dc&&a<=3?"success":"warning",o,"快速测试结果")}))},window.reloadSpellTemplates=E,"complete"===document.readyState||"interactive"===document.readyState?Xe():document.addEventListener("DOMContentLoaded",Xe);</script></body></html>