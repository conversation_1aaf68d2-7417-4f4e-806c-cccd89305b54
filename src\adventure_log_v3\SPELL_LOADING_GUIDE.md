# 法术加载系统使用指南 v3.0

## 🔮 概述

法术系统已完全重构，从硬编码方式改为从世界书 `Spell_Library.json` 中动态加载，并实现了高性能的localStorage缓存机制。系统支持多种加载策略和完整的错误处理，确保在不同情况下都能正常工作。

## 📚 世界书结构要求

### 方案1：完整法术库（推荐）
在世界书中创建一个条目，包含以下关键字之一：
- `AI_ALL_SPELLS` （主要关键字）
- `SPELLS_ALL`
- `所有法术`
- `ALL_SPELLS`
- `法术库`

条目内容应为完整的法术数组 JSON 格式，支持300+法术。

### 方案2：分级法术库（备用）
如果完整加载失败，系统会自动尝试按等级分别加载：
- `AI_CANTRIPS` - 戏法（0环）
- `AI_LEVEL_1_SPELLS` - 1环法术
- `AI_LEVEL_2_SPELLS` - 2环法术
- `AI_LEVEL_3_SPELLS` - 3环法术
- `AI_LEVEL_4_SPELLS` - 4环法术
- `AI_LEVEL_5_SPELLS` - 5环法术
- `AI_LEVEL_6_SPELLS` - 6环法术
- `AI_LEVEL_7_SPELLS` - 7环法术
- `AI_LEVEL_8_SPELLS` - 8环法术
- `AI_LEVEL_9_SPELLS` - 9环法术

## ⚡ 缓存机制

### localStorage缓存
- **首次加载**：从世界书加载并自动缓存到localStorage
- **后续加载**：直接从缓存读取（毫秒级加载）
- **版本控制**：缓存带有版本号，支持自动更新
- **错误处理**：缓存损坏时自动清除并重新加载

### 缓存键名
```
spellTemplates_v1.0
```

## 🔄 加载流程

1. **缓存检查**：首先检查localStorage中是否有有效缓存
2. **缓存加载**：如果缓存存在且有效，直接使用（快速加载）
3. **世界书加载**：缓存不存在时，从世界书加载
   - 尝试完整加载（`AI_ALL_SPELLS`）
   - 失败时尝试分级加载
   - 成功后自动缓存到localStorage
4. **空数组回退**：所有加载都失败时，返回空数组（不再有硬编码法术）

## 🚀 使用方法

### 自动加载
系统在初始化时会自动加载法术模板：
```typescript
// 在应用启动时自动调用
await loadSpellTemplates();
```

### 手动操作
可以通过全局对象进行各种操作：

#### 重新加载法术（优先使用缓存）
```javascript
window.spellSystem.reloadSpellTemplates()
```

#### 清除缓存并重新加载
```javascript
window.spellSystem.clearSpellCache()
```

#### 获取当前法术模板
```javascript
const allSpells = window.spellSystem.getSpellTemplates();
console.log(`当前加载了 ${allSpells.length} 个法术`);
```

#### 测试世界书连接
```javascript
// 测试特定世界书和关键字
window.spellSystem.testSpecificKey('Spell_Library.json', 'AI_ALL_SPELLS')

// 通用连接测试
window.spellSystem.testLorebookConnection()
```

## 📊 状态信息

系统会通过 toastr 显示详细的加载状态：

### 缓存相关
- ⚡ **info**: `从缓存快速加载 192 个法术` - 缓存命中
- 🗑️ **info**: `已清除法术缓存` - 缓存清除成功

### 世界书加载
- ✅ **success**: `从世界书加载 192 个法术并已缓存` - 首次加载成功
- ✅ **success**: `分级加载 192 个法术并已缓存` - 分级加载成功
- 📋 **info**: `使用默认法术模板，共 0 个法术` - 无世界书数据

### 错误状态
- ⚠️ **warning**: `法术加载失败，使用默认模板` - 加载失败
- ❌ **error**: `连接失败: 错误信息` - 连接错误

## 法术数据格式

法术数据应符合 `SpellTemplate` 接口：
```typescript
interface SpellTemplate {
  name_zh: string;
  name_en: string;
  level: number;
  school: string;
  casting_time: string;
  range: string;
  components: string[];
  duration: string;
  description_short: string;
  description_long: string;
  // ... 其他可选属性
}
```

## 🔧 故障排除

### 法术加载失败
1. **检查世界书文件名**：必须是 `Spell_Library.json`
2. **确认关键字**：主要使用 `AI_ALL_SPELLS`
3. **验证JSON格式**：确保法术数据是有效的JSON数组
4. **查看toastr消息**：观察具体错误信息
5. **测试连接**：使用 `window.spellSystem.testSpecificKey()` 测试

### 缓存问题
```javascript
// 清除损坏的缓存
window.spellSystem.clearSpellCache()

// 检查localStorage
console.log(localStorage.getItem('spellTemplates_v1.0'));

// 手动清除（如果需要）
localStorage.removeItem('spellTemplates_v1.0');
```

### 性能问题
如果加载过慢：
1. **使用缓存**：首次加载后会自动缓存
2. **分级加载**：系统会自动尝试分级加载
3. **减少数据量**：考虑分组存储大型法术库

### 调试命令集合
```javascript
// === 基础检查 ===
// 检查当前法术数量
console.log('当前法术数量:', window.spellSystem.getSpellTemplates().length);

// 查看特定法术
const fireball = window.spellSystem.getSpellTemplates().find(s => s.name_en === 'Fireball');
console.log('火球术:', fireball);

// === 重新加载 ===
// 重新加载（优先缓存）
window.spellSystem.reloadSpellTemplates()

// 强制从世界书重新加载
window.spellSystem.clearSpellCache()

// === 连接测试 ===
// 测试世界书连接
window.spellSystem.testLorebookConnection()

// 测试特定条目
window.spellSystem.testSpecificKey('Spell_Library.json', 'AI_ALL_SPELLS')

// === 缓存管理 ===
// 检查缓存状态
console.log('缓存状态:', localStorage.getItem('spellTemplates_v1.0') ? '存在' : '不存在');

// 查看缓存大小
const cache = localStorage.getItem('spellTemplates_v1.0');
console.log('缓存大小:', cache ? `${(cache.length / 1024).toFixed(2)} KB` : '无缓存');
```

## ⚠️ 重要注意事项

1. **世界书文件名**：必须严格为 `Spell_Library.json`
2. **关键字精确匹配**：`AI_ALL_SPELLS` 区分大小写
3. **JSON格式要求**：必须是有效的JSON数组格式
4. **缓存版本控制**：更新法术数据后需要清除缓存
5. **iframe重新渲染**：每次AI回复都会重新渲染，但缓存机制确保快速加载
6. **localStorage限制**：注意浏览器localStorage容量限制
7. **错误处理完整**：系统有完整的错误处理和回退机制

## 📈 性能特性

### 加载速度对比
- **首次加载**：~2-5秒（从世界书加载192个法术）
- **缓存加载**：~50-100毫秒（从localStorage读取）
- **iframe重新渲染**：缓存机制确保每次都是快速加载

### 内存使用
- **法术数据**：约100-200KB（192个法术）
- **缓存存储**：localStorage中约100-200KB
- **运行时内存**：最小化内存占用

## 🔄 更新历史

### v3.0 (当前版本)
- ✅ **完全重构**：从硬编码改为世界书加载
- ✅ **localStorage缓存**：高性能缓存机制
- ✅ **版本控制**：缓存版本管理
- ✅ **错误处理**：完整的错误处理和回退
- ✅ **调试工具**：丰富的调试和测试功能
- ✅ **性能优化**：iframe重新渲染性能优化
- ✅ **文档完善**：详细的使用指南和故障排除

### v2.x
- 基础世界书加载功能
- 分级加载策略

### v1.x
- 硬编码法术模板
