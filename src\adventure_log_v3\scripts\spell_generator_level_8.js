/**
 * 8环法术生成器
 * 基于AI知识库生成完整的DND5e 8环法术数据
 */

const fs = require('fs');

// 8环法术数据
const LEVEL_8_SPELLS = [
  {
    name_zh: '动物形态',
    name_en: 'Animal Shapes',
    level: 8,
    school: '变化',
    casting_time: '1 动作',
    range: '30尺',
    components: ['V', 'S'],
    duration: '专注，至多24小时',
    description_short: '将多个生物变为野兽。',
    description_long: '你的魔法将其他人变为野兽。选择射程内任意数量的自愿生物。你将每个目标变为一个大型或更小的野兽，其挑战等级为4或更低。',
    higher_level_cast: {
      per_slot_above_base: '每高于8环的法术位',
      effect: '可以影响一个额外的生物'
    }
  },
  {
    name_zh: '反魔法场',
    name_en: 'Antimagic Field',
    level: 8,
    school: '防护',
    casting_time: '1 动作',
    range: '自身（10尺半径球形）',
    components: ['V', 'S', 'M (一撮铁粉或铁屑)'],
    duration: '专注，至多1小时',
    description_short: '创造抑制魔法的区域。',
    description_long: '一个10尺半径的隐形球形反魔法场围绕着你。这个区域与任何魔法隔绝。在球形内，法术无法施展，召唤生物消失，魔法物品变为平凡物品。',
    area_of_effect: { type: '球形', size: '10尺半径' }
  },
  {
    name_zh: '嫌恶术/关怀术',
    name_en: 'Antipathy/Sympathy',
    level: 8,
    school: '惑控',
    casting_time: '1小时',
    range: '60尺',
    components: ['V', 'S', 'M (一块明矾浸泡在醋中用于嫌恶效果，或一滴蜂蜜用于关怀效果)'],
    duration: '10天',
    description_short: '使目标吸引或排斥特定生物类型。',
    description_long: '这个法术吸引或排斥你选择的生物类型。你指定一个你能看见的射程内目标，它必须是巨型或更小的生物或物体。'
  },
  {
    name_zh: '克隆术',
    name_en: 'Clone',
    level: 8,
    school: '死灵',
    casting_time: '1小时',
    range: '触及',
    components: ['V', 'S', 'M (一颗价值1000gp的钻石和至少1立方寸目标生物的血肉，法术消耗此材料)'],
    duration: '立即',
    description_short: '创造生物的克隆体。',
    description_long: '这个法术在一个密封容器中培养目标生物的惰性复制品，如大瓮。克隆体在120天后成熟。如果原始生物死亡，其灵魂会转移到克隆体中。'
  },
  {
    name_zh: '操控天气',
    name_en: 'Control Weather',
    level: 8,
    school: '变化',
    casting_time: '10分钟',
    range: '自身（5英里半径）',
    components: ['V', 'S', 'M (燃烧的熏香和一块土、一碗水和一根木棒)'],
    duration: '专注，至多8小时',
    description_short: '改变大范围区域的天气。',
    description_long: '你控制以你为中心5英里半径内的天气。你必须在户外施展此法术。移动到法术无法到达的地方会结束法术。',
    area_of_effect: { type: '球形', size: '5英里半径' }
  },
  {
    name_zh: '半位面',
    name_en: 'Demiplane',
    level: 8,
    school: '咒法',
    casting_time: '1 动作',
    range: '60尺',
    components: ['S'],
    duration: '1小时',
    description_short: '创造小型异次元空间。',
    description_long: '你在射程内创造一扇阴影门，通向一个你创造的半位面。半位面可以是30尺见方的房间，或者是30×20×10尺的走廊。'
  },
  {
    name_zh: '支配怪物',
    name_en: 'Dominate Monster',
    level: 8,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '专注，至多1小时',
    description_short: '控制一个怪物的行动。',
    description_long: '你试图魅惑射程内一个你能看见的生物。它必须成功进行感知豁免，否则在法术持续时间内被你魅惑。',
    save: { attribute: '感知', effect_on_success: '无效果' },
    higher_level_cast: {
      per_slot_above_base: '每高于8环的法术位',
      effect: '持续时间增加1小时'
    }
  },
  {
    name_zh: '地震术',
    name_en: 'Earthquake',
    level: 8,
    school: '塑能',
    casting_time: '1 动作',
    range: '500尺',
    components: ['V', 'S', 'M (一撮泥土、一块石头和一块粘土)'],
    duration: '专注，至多1分钟',
    description_short: '创造破坏性地震。',
    description_long: '你创造一次地震冲击，撼动射程内100尺半径的地面。在该区域内站在地面上的每个生物必须进行敏捷豁免。',
    save: { attribute: '敏捷', effect_on_success: '见描述' },
    area_of_effect: { type: '球形', size: '100尺半径' }
  },
  {
    name_zh: '花言巧语',
    name_en: 'Glibness',
    level: 8,
    school: '变化',
    casting_time: '1 动作',
    range: '自身',
    components: ['V'],
    duration: '1小时',
    description_short: '获得超凡的说服能力。',
    description_long: '直到法术结束，当你进行魅力检定时，你可以将d20的掷骰结果替换为15。此外，无论你说什么，探测你是否说谎的魔法都显示你在说真话。'
  },
  {
    name_zh: '圣洁灵光',
    name_en: 'Holy Aura',
    level: 8,
    school: '防护',
    casting_time: '1 动作',
    range: '自身',
    components: ['V', 'S', 'M (一个微型圣物匣，价值1000gp)'],
    duration: '专注，至多1分钟',
    description_short: '保护盟友免受邪恶伤害。',
    description_long: '神圣光芒从你身上散发，形成30尺半径的柔和光辉。你和光辉内的友善生物在所有属性豁免上具有优势。',
    area_of_effect: { type: '球形', size: '30尺半径' }
  },
  {
    name_zh: '焚云术',
    name_en: 'Incendiary Cloud',
    level: 8,
    school: '咒法',
    casting_time: '1 动作',
    range: '150尺',
    components: ['V', 'S'],
    duration: '专注，至多1分钟',
    description_short: '创造移动的火焰云团。',
    description_long: '一团翻滚的云雾出现在射程内一点。云雾在20尺半径的球形区域内蔓延，绕过拐角。云雾在法术持续时间内保持存在。',
    damage: '10d8',
    damage_type: '火焰',
    save: { attribute: '敏捷', effect_on_success: '伤害减半' },
    area_of_effect: { type: '球形', size: '20尺半径' }
  },
  {
    name_zh: '迷宫术',
    name_en: 'Maze',
    level: 8,
    school: '咒法',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V', 'S'],
    duration: '专注，至多10分钟',
    description_short: '将目标困在异次元迷宫中。',
    description_long: '你将一个射程内你能看见的生物放逐到一个迷宫般的半位面中。目标在那里保持至法术结束或它逃脱迷宫。',
    save: { attribute: '智力', effect_on_success: '可以尝试逃脱' }
  },
  {
    name_zh: '心灵屏障',
    name_en: 'Mind Blank',
    level: 8,
    school: '防护',
    casting_time: '1 动作',
    range: '触及',
    components: ['V', 'S'],
    duration: '24小时',
    description_short: '保护免受心灵探测和魅惑。',
    description_long: '直到法术结束，一个你触摸的自愿生物免疫任何探知其位置的预言魔法，以及任何魅惑、预言或惑控学派的法术。'
  },
  {
    name_zh: '律令震慑',
    name_en: 'Power Word Stun',
    level: 8,
    school: '惑控',
    casting_time: '1 动作',
    range: '60尺',
    components: ['V'],
    duration: '立即',
    description_short: '用一个词语震慑敌人。',
    description_long: '你说出一个力量之词，可以压倒射程内一个你能看见的生物的心智，使其震慑。如果目标有150生命值或更少，它被震慑。'
  },
  {
    name_zh: '阳炎爆',
    name_en: 'Sunburst',
    level: 8,
    school: '塑能',
    casting_time: '1 动作',
    range: '150尺',
    components: ['V', 'S', 'M (火和一块阳光石)'],
    duration: '立即',
    description_short: '创造致盲的阳光爆发。',
    description_long: '明亮的阳光在射程内60尺半径的球形区域内闪烁。每个在该区域内的生物必须进行体质豁免。',
    damage: '12d6',
    damage_type: '光耀',
    save: { attribute: '体质', effect_on_success: '伤害减半，不致盲' },
    area_of_effect: { type: '球形', size: '60尺半径' }
  },
  {
    name_zh: '心灵感应',
    name_en: 'Telepathy',
    level: 8,
    school: '塑能',
    casting_time: '1 动作',
    range: '无限',
    components: ['V', 'S', 'M (一对连接的银戒指)'],
    duration: '24小时',
    description_short: '与熟悉的生物建立心灵联系。',
    description_long: '你与一个你熟悉的生物建立心灵感应联系。该生物必须在与你相同的存在位面上。'
  },
  {
    name_zh: '海啸术',
    name_en: 'Tsunami',
    level: 8,
    school: '咒法',
    casting_time: '1分钟',
    range: '视线',
    components: ['V', 'S'],
    duration: '专注，至多6轮',
    description_short: '召唤巨大的海啸波浪。',
    description_long: '一堵高达300尺、宽达300尺、厚达50尺的水墙出现在射程内一点。墙持续至法术结束。',
    area_of_effect: { type: '墙形', size: '300尺高×300尺宽×50尺厚' }
  }
];

/**
 * 生成8环法术世界书
 */
function generateLevel8WorldBook() {
  const worldBook = {
    entries: {
      5008: {
        uid: 5008,
        key: ["AI_LEVEL_8_SPELLS", "AI八环法术", "DND5E_AI_LEVEL_8", "AI_SPELLS_LEVEL_8"],
        keysecondary: [],
        comment: `AI生成的DND5e八环法术完整数据 (${LEVEL_8_SPELLS.length}个法术)`,
        content: JSON.stringify(LEVEL_8_SPELLS, null, 2),
        constant: true,
        vectorized: false,
        selective: true,
        selectiveLogic: 0,
        addMemo: true,
        order: 100,
        position: 0,
        disable: false,
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: false,
        probability: 100,
        useProbability: true,
        depth: 4,
        group: "",
        groupOverride: false,
        groupWeight: 100,
        scanDepth: null,
        caseSensitive: null,
        matchWholeWords: null,
        useGroupScoring: null,
        automationId: "",
        role: null,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        displayIndex: 5008
      }
    }
  };
  
  const outputPath = 'AI_DND5e_Level8_Complete.json';
  fs.writeFileSync(outputPath, JSON.stringify(worldBook, null, 2), 'utf-8');
  console.log(`生成8环法术世界书: ${outputPath} (${LEVEL_8_SPELLS.length}个法术)`);
  
  return LEVEL_8_SPELLS;
}

// 执行生成
if (require.main === module) {
  console.log('=== 8环法术生成器 ===');
  generateLevel8WorldBook();
  console.log('8环法术世界书生成完成！');
}

module.exports = { LEVEL_8_SPELLS, generateLevel8WorldBook };
