## 武器模块设计文档 (Adventure Log v3)

**版本**: 1.0
**日期**: 2025-06-01

### 1. 引言与目标

#### 1.1. 项目背景
本文档是“冒险日志 v3”项目的一部分，专注于武器模块的设计与实现。武器是战斗系统的核心组成部分，其属性和效果直接影响战斗的流程和结果。

#### 1.2. 模块目标
1.  **精确表示武器**: 定义清晰的数据结构，能够准确描述标准武器和魔法武器的各项属性。
2.  **集成D&D 5e规则**: 确保武器的攻击检定、伤害计算、属性效果等符合D&D 5e核心规则。
3.  **模块化魔法效果**: 设计一个灵活的魔法效果系统，能够方便地为武器添加和处理各种常见的魔法增强（如攻击/伤害加值、附加元素伤害、命中时触发效果等）。
4.  **客户端驱动逻辑**: 强调在客户端处理大部分武器相关的计算和规则逻辑，AI负责叙事和提供数据。
5.  **可扩展性**: 为未来可能引入的更复杂武器特性（如武器精通、神器级效果）奠定基础。

### 2. 核心数据结构

#### 2.1. `WeaponTemplate` (源自 `weapon_templates.md`)
此数据结构定义了非魔法标准武器的基础属性。客户端在初始化时加载这些模板。

```typescript
// (已在 weapon_templates.md 中定义，此处为回顾)
interface WeaponTemplate {
  name_zh: string;        // "长剑"
  name_en: string;        // "Longsword"
  category: string;       // "军用近战"
  damage: string;         // "1d8"
  damageType: string;     // "挥砍"
  properties: string[];   // ["多用 (1d10)"]
  weight: string;         // "3磅"
  cost: string;           // "15 GP"
  mastery?: string;       // (可选) "削弱"
}
```

#### 2.2. `EquipmentItem` (针对武器的细化)
此接口定义了玩家实际持有或装备的物品，当 `type` 为 "武器" 时，它将包含武器特有的属性，并可能引用一个 `WeaponTemplate`。

```typescript
// (位于 src/adventure_log_v3/index.ts，针对武器的关注点)
interface EquipmentItem {
  id: string;                   // 物品的唯一ID，例如 "longsword_flame_tongue_01"
  name: string;                 // 物品的显示名称，例如 "焰舌长剑"
  baseItemName?: string;         // 指向 WeaponTemplate 的 name_zh 或 name_en，例如 "长剑"
  type: "武器" | "盔甲" | "盾牌" | "戒指" | "护符" | "奇物" | string; // 明确为 "武器"
  equipped?: boolean;            // 是否已装备（主手、副手等概念可后续扩展）
  description?: string;          // 物品描述，包括魔法效果的文字说明

  // --- 从 WeaponTemplate 继承或直接定义的基础物理属性 ---
  damage?: string;               // (可继承) "1d8"
  damageType?: string;           // (可继承) "挥砍"
  properties?: string[];         // (可继承) ["多用 (1d10)"]
  // 其他如 category, weight, cost 可按需存储或仅在模板中

  // --- 魔法效果 ---
  magicEffects?: MagicEffect[];  // 魔法效果列表
}
```

#### 2.3. `MagicEffect` (武器相关效果的细化)
此接口定义了可以附加到武器上的各种魔法效果。

```typescript
// (位于 src/adventure_log_v3/index.ts，针对武器的关注点)
interface MagicEffect {
  type: EffectType;
  value: any;
  condition?: string; // 例如 "仅对不死生物生效"
  notes?: string;
  // sourceId, duration 等字段按需添加
}

// 武器模块重点关注的 EffectType (示例，可扩展):
type EffectType =
  | "ATTACK_BONUS"          // 攻击检定加值 (value: number)
  | "DAMAGE_BONUS_STATIC"   // 固定伤害加值 (value: { amount: number, type?: string }) 
                            // type可选，若无则为武器基础伤害类型
  | "DAMAGE_BONUS_DICE"     // 附加伤害骰 (value: { dice: string, type: string })
  | "ON_HIT_EFFECT_SAVE"    // 命中时触发豁免 (value: { saveAttribute: string, dc: number, effectOnFail: string, effectOnSuccess?: string })
  | "ON_HIT_EFFECT_CONDITION" // 命中时施加状态 (value: { condition: string, duration?: string, saveDC?: number, saveAttribute?: string })
  | "ON_HIT_EFFECT_DAMAGE"  // 命中时触发额外伤害 (类似 DAMAGE_BONUS_DICE，但可能更复杂或有条件)
  | "CRITICAL_HIT_MODIFIER" // 重击效果调整 (value: { additionalDice?: string, extraEffect?: string })
  | "WEAPON_PROPERTY_GRANT" // 赋予武器额外属性 (value: string, e.g., "灵巧", "触及")
  | "WEAPON_PROPERTY_MODIFY"// 修改武器现有属性 (e.g., 改变伤害骰或类型)
  | "SPELL_LIKE_ABILITY"    // 提供类法术能力 (value: { spellName: string, uses: number, per: string, actionType?: string })
  | "ATTRIBUTE_REQUIREMENT" // 装备或有效使用此武器的属性需求 (value: { attribute: string, minimum: number })
  // ... 其他如吸血、特殊材料效果等
```
*   **`DAMAGE_BONUS_STATIC`**: 用于如 "+1 武器" 提供的固定伤害加值。
*   **`DAMAGE_BONUS_DICE`**: 用于如 "焰舌" 提供的额外元素伤害骰。

### 3. 核心武器机制与魔法效果集成

#### 3.1. 武器实例化与属性来源
1.  **获取基础武器**: 当玩家获得一把武器时（例如 "长剑"），其基础物理属性（伤害骰、类型、标准属性如“多用”）从对应的 `WeaponTemplate` 加载。
2.  **赋予魔法效果**:
    *   如果获得的是魔法武器（例如 "焰舌长剑"），则 `baseItemName` 仍为 "长剑"。
    *   其 `magicEffects` 数组将包含该武器特有的魔法效果。例如：
        ```json
        "magicEffects": [
          { "type": "ATTACK_BONUS", "value": 1 }, // 假设是+1武器
          { "type": "DAMAGE_BONUS_STATIC", "value": { "amount": 1 } }, // +1伤害
          { "type": "DAMAGE_BONUS_DICE", "value": { "dice": "2d6", "type": "火焰" } } // 焰舌的火焰伤害
        ]
        ```
    *   `name` 字段应反映其魔法特性，如 "焰舌长剑+1"。

#### 3.2. 战斗中应用逻辑的顺序（概念性）
1.  **选择武器进行攻击**。
2.  **攻击检定**:
    *   基础属性调整值（力量或敏捷，考虑“灵巧”）。
    *   熟练加值（如果角色熟练该武器类型）。
    *   应用来自武器 `magicEffects` 的 `ATTACK_BONUS`。
    *   应用其他来源的加值/减值（如法术、状态）。
3.  **命中判定**: 结果 vs 目标 AC。
4.  **伤害计算 (如果命中)**:
    *   **基础伤害**: 从武器模板获取伤害骰 (考虑“多用”等属性)，掷骰。
    *   **重击**: 如果攻击检定是重击 (天然20)，则将所有基础伤害骰和部分魔法伤害骰（根据规则书，通常是武器自身特性带来的附加伤害骰，而非所有魔法效果的骰子）掷两次。`CRITICAL_HIT_MODIFIER` 效果在此处理。
    *   **属性调整值**: 将用于攻击检定的属性调整值加到伤害上 (除非武器属性另有规定，如副手攻击不加调整值)。
    *   **魔法固定伤害加值**: 应用来自 `magicEffects` 的 `DAMAGE_BONUS_STATIC`。
    *   **魔法附加伤害骰**: 掷算来自 `magicEffects` 的 `DAMAGE_BONUS_DICE`。这些通常不因重击而双倍，除非效果特别说明。
    *   **汇总伤害**: 将所有伤害按类型汇总。
    *   **应用目标抗性/易伤/免疫**。
5.  **处理命中时效果**:
    *   检查武器的 `magicEffects` 中是否有 `ON_HIT_EFFECT_SAVE` 或 `ON_HIT_EFFECT_CONDITION` 等。
    *   如果存在，则触发相应逻辑（例如，通知AI目标需要进行豁免检定）。

### 4. 武器相关魔法效果分类详解

*   **`ATTACK_BONUS`**:
    *   **处理**: 在攻击检定计算时，直接将 `value` (通常为+1, +2, +3) 加入总值。
*   **`DAMAGE_BONUS_STATIC`**:
    *   **处理**: 在伤害计算的属性调整值之后，将 `value.amount` 加入总伤害。如果 `value.type` 指定了类型，则该部分伤害具有此类型。
*   **`DAMAGE_BONUS_DICE`**:
    *   **处理**: 在计算完基础伤害和固定加值后，额外掷 `value.dice`，并将此伤害（类型为 `value.type`）加入总伤害。
*   **`ON_HIT_EFFECT_SAVE`**:
    *   **处理**: 攻击命中后，客户端识别此效果。构造提示给AI，包含豁免属性 (`value.saveAttribute`) 和豁免DC (`value.dc`)，告知AI目标需要进行豁免。AI负责判定豁免结果并描述后续效果 (`value.effectOnFail`, `value.effectOnSuccess`)。
*   **`ON_HIT_EFFECT_CONDITION`**:
    *   **处理**: 攻击命中后，如果此效果不需要目标豁免或豁免已失败（可能由 `ON_HIT_EFFECT_SAVE` 触发），则客户端通知AI目标将承受 `value.condition` 状态，持续时间为 `value.duration`。AI负责更新目标状态并叙述。
*   **`CRITICAL_HIT_MODIFIER`**:
    *   **处理**: 在判定为重击时，除了标准双倍伤害骰规则外，应用此效果。例如，野蛮人的“野蛮重击”特性可以视为此类效果，增加额外的重击伤害骰。
*   **`WEAPON_PROPERTY_GRANT` / `WEAPON_PROPERTY_MODIFY`**:
    *   **处理**: 在武器实例化或装备时，这些效果会修改武器的基础 `properties` 数组或伤害骰/类型。例如，一个魔法效果可以将普通长剑变为具有“灵巧”属性。这会影响攻击和伤害计算时选择哪个属性调整值。
*   **`SPELL_LIKE_ABILITY`**:
    *   **处理**: 客户端在生成行动选项时，检查已装备武器是否提供此类能力且尚有使用次数。如果可用，则生成一个代表使用此能力的选项。选择后，触发类似法术施放的流程。

### 5. 客户端核心逻辑 (`src/adventure_log_v3/index.ts`)

*   **`performCheck` (攻击检定部分)**:
    *   必须能从 `playerState.equipment` 中获取当前攻击所用武器的实例。
    *   读取武器的 `baseItemName` 以获取模板属性（如是否“灵巧”以决定使用力量或敏捷）。
    *   读取武器实例的 `magicEffects`，查找并应用所有 `ATTACK_BONUS` 类型的效果。
*   **`calculateDamageRoll` (或扩展的伤害计算函数)**:
    *   参数应包括：攻击命中的武器实例 (`EquipmentItem`)，是否重击 (`boolean`)。
    *   **步骤**:
        1.  获取武器基础伤害骰和类型 (来自模板或实例的 `damage`, `damageType`)。
        2.  处理“多用”等影响基础伤害骰的武器属性。
        3.  掷基础伤害骰。
        4.  **重击处理**: 如果是重击，根据规则（通常是所有伤害骰翻倍，包括武器自身特性带来的附加骰，但不包括大部分后加的魔法效果骰）和武器的 `CRITICAL_HIT_MODIFIER` 效果，调整骰子数量。
        5.  应用攻击属性调整值。
        6.  应用武器 `magicEffects` 中的 `DAMAGE_BONUS_STATIC`。
        7.  掷算并累加武器 `magicEffects` 中的所有 `DAMAGE_BONUS_DICE`。
        8.  返回一个包含总伤害值和伤害类型（可能是多种）的对象或数组。
*   **`handleActionChoice` (处理攻击选项后)**:
    *   在攻击命中后，检查当前武器的 `magicEffects` 是否有 `ON_HIT_EFFECT_SAVE` 或 `ON_HIT_EFFECT_CONDITION`。
    *   如果有，则在发送给AI的反馈中包含这些触发信息，以便AI进行后续处理。
*   **武器属性应用**:
    *   **灵巧 (Finesse)**: 在选择攻击和伤害属性时，比较力量和敏捷调整值。
    *   **多用 (Versatile)**: 如果玩家双手使用，客户端应使用更大的伤害骰。这可能需要UI提示或玩家明确选择。
    *   **投掷 (Thrown)**: 允许近战武器进行远程攻击，使用与近战相同的属性。
    *   **射程 (Range)**: 远程攻击时，判断正常射程和远射程（劣势）。
    *   **弹药 (Ammunition)**: 客户端需要追踪弹药数量。若无弹药，则无法使用该武器攻击。
    *   **装填 (Loading)**: 限制每回合的攻击次数。
    *   **重型 (Heavy)**: 
        *   **计划实现**: 如果小型角色（例如，种族为“半身人”或“侏儒”）使用具有“重型”属性的武器，其攻击检定将具有劣势。
        *   **客户端逻辑 (`handleActionChoice`)**: 在玩家选择使用武器进行攻击时，将检查玩家的 `playerState.race` 是否为小型种族，并检查武器的 `effectiveProperties` 是否包含“重型”。如果两者都满足，则在调用 `performCheck` 时传递 `advantageState: 'disadvantage'`。
        *   **客户端逻辑 (`performCheck`)**: `performCheck` 函数将修改以接受 `advantageState` 参数。如果状态为 `'disadvantage'`，则d20掷骰将执行两次并取较低值。检定结果的反馈（如toast提示和发送给AI的文本）也将注明劣势情况。
    *   其他属性按规则实现。

### 6. AI 交互

*   **AI提供武器数据**:
    *   当玩家获得新武器（尤其是魔法武器）时，AI应通过 `variableUpdates` 指令，使用详细的 `EquipmentItem` JSON结构（包含 `baseItemName` 和 `magicEffects`）将其添加到玩家的物品栏或装备中。
    *   AI的叙述应与武器的魔法特性相符。
*   **AI响应命中后效果**:
    *   当客户端通知AI一个 `ON_HIT_EFFECT_SAVE` 被触发时，AI负责判定目标（通常是敌人）的豁免结果，并描述相应的效果（如中毒、恐惧等状态的施加，或伤害的发生）。
    *   AI根据这些效果更新其内部维护的敌人状态。

### 7. 未来考虑与可扩展性

*   **武器精通 (Mastery)**: PHB 2024引入的武器精通属性，效果各异，需要在各自的触发时机（如攻击、命中、失手等）应用其逻辑。
*   **更复杂的条件效果**: 例如，“仅在白天生效”、“对特定种族敌人造成双倍伤害”等，需要在 `MagicEffect.condition` 字段和客户端逻辑中支持更复杂的条件判断。
*   **充能与次数限制**: 对于 `SPELL_LIKE_ABILITY` 或某些 `ON_HIT_EFFECT`，需要更完善的充能/使用次数追踪机制。
*   **组合效果**: 一件武器可能同时拥有多种魔法效果，处理逻辑需要能正确叠加或按优先级应用它们。
*   **动态效果变化**: 某些神器的效果可能会随剧情或角色行为而改变或解锁。

### 8. 实施计划 (高层步骤，非编码)

1.  **阶段一: 数据结构与基础模板**
    *   **任务**: 在 `src/adventure_log_v3/index.ts` 中正式定义和完善 `EquipmentItem` (针对武器) 和 `MagicEffect` 接口。
    *   **任务**: 确认 `weapon_templates.md` 的内容和格式，编写加载和查询这些模板的客户端辅助函数。
    *   **任务**: 更新 `PlayerState` 以正确存储武器实例。
2.  **阶段二: 核心攻击与伤害加值**
    *   **任务**: 修改客户端攻击检定逻辑，以包含来自武器的 `ATTACK_BONUS`。
    *   **任务**: 修改客户端伤害计算逻辑，以包含来自武器的 `DAMAGE_BONUS_STATIC` 和 `DAMAGE_BONUS_DICE`。
    *   **任务**: 实现基础武器属性（灵巧、多用、投掷、射程）在攻击和伤害计算中的应用。
    *   **任务**: 更新AI提示，使其能生成带有这些基础魔法效果的武器，并通过 `variableUpdates` 给予玩家。
3.  **阶段三: 命中时触发效果 (豁免型)**
    *   **任务**: 实现客户端在武器命中后，识别 `ON_HIT_EFFECT_SAVE`，并向AI发送目标豁免请求的逻辑。
    *   **任务**: 更新AI提示，使其能正确响应豁免请求，判定结果并描述效果。
4.  **阶段四: 其他魔法效果与进阶武器属性**
    *   **任务**: 逐步实现其他类型的 `MagicEffect`，如 `SPELL_LIKE_ABILITY`, `ON_HIT_EFFECT_CONDITION`, `CRITICAL_HIT_MODIFIER` 等。
    *   **任务**: 逐步实现更复杂的标准武器属性（如弹药追踪、装填）。
    *   **任务**: (远期) 考虑武器精通属性的实现。
5.  **阶段五: 测试与迭代**
    *   **任务**: 针对每种实现的武器效果和属性进行充分测试。
    *   **任务**: 根据测试结果和反馈进行迭代优化。

---

此武器模块设计文档旨在为后续的开发工作提供一个清晰、详细的指引。通过分阶段实施，我们可以逐步构建一个功能完善且符合D&D规则的武器系统。



**目前已完成的工作总结 (针对武器与装备模块，基于 `src/adventure_log_v3/index.ts` 的最新状态):**

1.  **数据结构定义与更新**:
    *   `EquipmentItem` 接口已更新，包含了 `id`, `baseItemName`, `magicEffects`，以及可选的 `damage` 和 `damageType` 字段，以支持直接在物品实例上定义或覆盖基础物理属性。
    *   `InventoryItem` 接口已更新，添加了 `id`, `baseItemName`, `properties`, `magicEffects` 等可选字段，使其结构与 `EquipmentItem` 更一致，便于物品在装备和物品栏之间的转换及统一处理。
    *   `MagicEffect` 接口和 `EffectType` 类型已定义，涵盖了多种常见的魔法效果类型，如攻击加值、伤害加值（固定和骰子）、命中时触发豁免等。
    *   `WeaponTemplate` 接口已定义，用于从 `weapon_templates.md` 加载标准武器数据。
    *   `PlayerState` 中的 `equipment` 和 `inventory` 字段现在使用更新后的 `EquipmentItem` 和 `InventoryItem` 类型。
    *   `getDefaultPlayerState()` 函数中的默认装备和物品已更新，以包含新的 `id` 和 `baseItemName` (适用时)。

2.  **武器模板加载**:
    *   实现了一个 `loadWeaponTemplates()` 函数，用于在游戏初始化时 (`onMounted`) 从 `src/adventure_log_v3/weapon_templates.md` 文件中读取和解析武器模板数据，并存储到模块级的 `weaponTemplates` 数组中。该函数包含了对Markdown中JSON代码块的提取尝试。
    *   **重要说明（关于打包环境）**: 在最终的单HTML文件部署形态下，此处的“读取”并非运行时动态访问文件系统。实际上，`weapon_templates.md` 这类外部数据文件的内容会在项目**编译打包阶段**被读取，并作为字符串或JavaScript对象直接内联到最终的脚本代码中。因此，`loadWeaponTemplates` 函数在运行时操作的是这些已经嵌入到代码内部的数据，而不是去执行一个实时的外部文件读取。代码中出现的 `/read_file` 斜杠命令，在开发环境中可能用于直接读取源文件以方便调试，但在打包后的生产环境中，它要么被编译过程中的其他机制所替代（例如，替换为直接访问内联数据），要么其在SillyTavern扩展上下文中的行为被重定义为访问扩展包内的资源。

3.  **核心战斗逻辑初步集成**:
    *   **攻击检定 (`performCheck`)**:
        *   函数签名已更新，可接收 `attackingWeapon` 参数。
        *   已实现逻辑：在计算攻击总值时，会检查 `attackingWeapon` 的 `magicEffects` 并累加所有 `ATTACK_BONUS` 类型的效果值。
    *   **伤害计算 (`calculateDamageRoll`)**:
        *   新增了 `parseDamageString` 和 `rollDice` 辅助函数。
        *   新增了 `DamageRollResult` 接口。
        *   实现了 `calculateDamageRoll` 函数，能够：
            *   从武器实例或通过 `baseItemName` 从加载的 `weaponTemplates` 获取基础伤害骰和类型。
            *   处理基础伤害掷骰。
            *   应用重击规则（目前仅针对基础伤害骰翻倍）。
            *   加入攻击属性调整值。
            *   加入来自武器 `magicEffects` 的 `DAMAGE_BONUS_STATIC` (固定伤害加值)。
            *   加入来自武器 `magicEffects` 的 `DAMAGE_BONUS_DICE` (附加伤害骰，目前默认不因重击翻倍)。
            *   返回包含总伤害、组合伤害类型、详细计算过程和结构化伤害明细的 `DamageRollResult` 对象。
    *   **玩家行动处理 (`handleActionChoice`)**:
        *   **攻击检定反馈**: 在玩家选择攻击选项后，会调用更新后的 `performCheck` 函数，并将武器对象传递过去。生成的检定反馈信息 (`rollDetails`) 现在会包含来自武器的攻击加值。
        *   **伤害计算与反馈**: 如果攻击命中且是武器攻击，会调用 `calculateDamageRoll` 计算伤害，并将详细的伤害结果（总量、类型、计算过程）附加到 `checkFeedbackToAI` 字符串中，同时通过 `safeToastr` 和在主叙事区显示给玩家。
        *   **命中时触发豁免效果 (`ON_HIT_EFFECT_SAVE`)**: 如果命中的武器具有此效果，会在 `checkFeedbackToAI` 中附加一段文本，提示AI目标需要进行特定DC和属性的豁免检定。
    *   **物品获取/失去 (`applyVariableUpdate`)**:
        *   "物品获得" 逻辑已更新，现在能够从 `value` 对象中完整地复制包括 `id`, `baseItemName`, `properties`, `magicEffects` 在内的所有 `InventoryItem` 字段到玩家的物品栏。如果物品已存在（通过 `id` 或 `name` 判断），则增加数量并可能更新其他属性。
        *   "物品失去" 逻辑已更新，优先通过 `id` 查找要移除的物品。

4.  **武器属性处理辅助函数**:
    *   实现了 `getEffectiveWeaponProperties(weapon: EquipmentItem | undefined): string[]` 函数，该函数能够：
        *   从武器模板（如果 `baseItemName` 有效且模板已加载）获取基础属性。
        *   如果无模板或 `baseItemName`，则回退到武器实例上直接定义的 `properties`。
        *   应用来自 `magicEffects` 的 `WEAPON_PROPERTY_GRANT` 效果，动态添加属性。
        *   返回去重后的有效属性列表。
    *   `handleActionChoice` 中判断“灵巧”属性的逻辑已更新为使用此辅助函数。

**目前尚未完成的工作 (根据“武器模块设计文档.md”和我们的讨论):**

1.  **核心武器机制与魔法效果集成 (深化)**:
    *   **`calculateDamageRoll` 中的 `TODO`**:
        *   **多用 (Versatile) 属性**: 实现根据玩家选择（例如，通过UI或特定行动选项）来决定是使用单手伤害骰还是双手伤害骰的逻辑。目前总是使用基础伤害骰。
        *   **重击对魔法附加骰的影响**: 当前 `DAMAGE_BONUS_DICE` 默认不因重击翻倍。需要根据D&D 5e规则（PHB p.196 "Critical Hits"提到 "If the attack involves other damage dice... you roll those dice twice as well."）进一步明确哪些类型的附加伤害骰应该在重击时翻倍，并可能需要在 `MagicEffect` 中添加一个标志来控制此行为。
    *   **其他武器属性的完整应用**:
        *   **投掷 (Thrown)**: 客户端需要处理射程（正常/远距劣势）、近战中使用远程攻击的劣势等。目前仅属性选择正确。
        *   **射程 (Range)**: 远程武器的射程规则（正常/远距劣势/超出则失手）尚未在客户端检定中实现，这需要距离和位置信息。
        *   **弹药 (Ammunition)**:
            *   `getAmmunitionType` 已添加，但 `handleActionChoice` 中实际检查玩家物品栏中是否有对应弹药、并在攻击前阻止（如果弹药不足）的逻辑尚未完全集成。
            *   AI通过 `variableUpdates` 消耗弹药的机制需要AI提示词配合。
        *   **装填 (Loading)**: 限制每回合攻击次数的逻辑，与多打（Extra Attack）等特性相关，尚未实现。
        *   **其他PHB核心属性**: 如重型 (Heavy)、轻型 (Light，与双武器战斗相关)、触及 (Reach)、双手 (Two-Handed) 等，它们对战斗的具体影响（如小型生物使用重型武器劣势、双武器战斗规则、攻击范围等）需要在相应的客户端逻辑中体现。
        *   **武器精通 (Mastery)**: PHB 2024 新增内容，如削弱 (Sap)、失衡 (Topple) 等，其具体效果和触发时机需要在客户端实现。
    *   **`WEAPON_PROPERTY_MODIFY` 效果**: `getEffectiveWeaponProperties` 中有TODO，用于处理修改或移除武器现有属性的魔法效果。

2.  **其他类型的魔法效果处理**:
    *   **`ON_HIT_EFFECT_CONDITION`**: 命中时施加状态（如中毒、目眩），需要客户端通知AI，AI更新敌人状态并叙述。
    *   **`CRITICAL_HIT_MODIFIER`**: 除了标准的双倍伤害骰外，处理额外的重击效果。
    *   **`SPELL_LIKE_ABILITY`**: 客户端在生成行动选项时检查并提供使用此类能力的选项，处理使用次数。
    *   **`ATTRIBUTE_REQUIREMENT`**: 装备或有效使用武器的属性需求检查。
    *   **更广泛的装备效果**: 如 `AC_BONUS`, `SAVE_BONUS`, `SKILL_BONUS`, `PASSIVE_EFFECT` (如抗性/易伤/免疫、特殊感官), `MOVEMENT_MODIFIER`, `HEALING_EFFECT`, `RESOURCE_MODIFIER` 等，这些效果需要在各自相关的计算点（如更新AC、执行豁免、技能检定、承受伤害、移动、治疗、资源消耗/恢复）被读取和应用。

3.  **AI 交互的完善**:
    *   **AI提供魔法物品**: 需要确保AI能够按照更新后的 `EquipmentItem` (包含 `magicEffects`) 结构，通过 `variableUpdates` 指令正确地给予玩家魔法武器和装备。
    *   **AI响应命中后效果**: AI需要能够理解并响应客户端发送的关于命中时触发效果（如目标豁免）的提示，并据此更新敌人状态和游戏叙事。
    *   **AI对武器属性的运用**: 指导AI在叙述敌人行动时，能够体现其武器的特殊属性（如触及、装填等对战术的影响）。

4.  **UI 显示**:
    *   虽然主要关注逻辑，但长远来看，玩家信息界面（特别是详细角色卡）需要能够清晰展示武器的魔法效果、有效属性等。
    *   可能需要为具有类法术能力的物品提供使用界面。

5.  **测试与迭代**:
    *   对已实现的各种武器属性和魔法效果进行全面测试，确保其符合预期和D&D规则。
    *   根据测试结果和反馈进行迭代优化。

总的来说，我们已经为武器和装备模块（特别是魔法效果）的数据表示和部分核心计算逻辑奠定了坚实的基础。接下来的工作将是逐步实现和集成更多具体的武器属性和魔法效果类型，并不断完善AI的交互和客户端的处理逻辑。
