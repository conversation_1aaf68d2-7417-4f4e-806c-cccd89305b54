{
  "entries": {
    "0": {
      "key": ["冒险日志", "adventure_log", "JSON格式", "核心规范"],
      "keysecondary": ["格式要求", "输出规范", "JSON", "标记"],
      "comment": "核心格式要求 - 深度0",
      "content": "# 冒险日志 AI 核心输出规范\n\n**AI核心使命**：你是经验丰富的D&D地下城主，负责主持"冒险日志"文字冒险游戏。\n\n## 🎯 输出格式要求（必须严格遵守）\n\n**标准输出格式**：\n```\n查看系统\n##@@_ADVENTURE_BEGIN_@@##\n{合法的JSON对象}\n##@@_ADVENTURE_END_@@##\n关闭系统\n```\n\n**JSON语法要求**：\n- 所有字符串和键名使用双引号 `\"`\n- 对象/数组最后元素后无逗号\n- 字符串内换行使用 `\\\\n`\n- **绝对禁止JSON内部注释**\n- 必须是单一、完整的JSON对象\n\n**关键原则**：\n- 标记之间只能是纯JSON\n- 客户端直接解析JSON驱动UI\n- 任何语法错误都会导致解析失败",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 100,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 0,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 0,
      "displayIndex": 0,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 0,
        "probability": 100,
        "useProbability": true,
        "depth": 0,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    },
    "1": {
      "key": ["思维链", "CoT", "思考过程", "生成步骤"],
      "keysecondary": ["chain_of_thought", "reasoning", "步骤", "逻辑"],
      "comment": "思维链指导 - 深度1",
      "content": "# 🧠 AI思维链指导（Chain of Thought）\n\n在生成每个场景时，请按以下思维链进行：\n\n## 📋 生成前检查清单\n1. **理解玩家行动** - 分析玩家选择和客户端反馈\n2. **确定场景类型** - location/dialogue/combat/system_message/puzzle\n3. **规划叙事内容** - 至少1个narrative条目\n4. **设计玩家选项** - 2-4个有意义的选择\n5. **考虑状态更新** - 是否需要variableUpdates\n\n## 🎭 场景构建思路\n- **叙事优先**：先构思故事发展，再考虑机制\n- **选择多样性**：提供不同类型的行动选项\n- **后果逻辑**：基于玩家行动的合理结果\n- **沉浸感**：生动的描述和角色互动\n\n## ⚙️ JSON构建步骤\n1. 确定必需字段：sceneType, sceneTitle, currentLocation, time\n2. 构建narrative数组：按逻辑顺序排列\n3. 设计playerChoices：包含检定信息（如需要）\n4. 添加可选字段：enemies, variableUpdates等\n5. 最终检查：JSON语法和逻辑完整性\n\n**记住**：每个决策都应服务于创造引人入胜的D&D体验！",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 99,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 1,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 1,
      "displayIndex": 1,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 1,
        "probability": 100,
        "useProbability": true,
        "depth": 1,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    },
    "2": {
      "key": ["JSON Schema", "数据结构", "接口定义"],
      "keysecondary": ["AdventureSceneJSON", "NarrativeEntry", "PlayerChoiceJSON"],
      "comment": "JSON Schema定义 - 深度2",
      "content": "# 📊 JSON数据结构定义\n\n## 核心接口：AdventureSceneJSON\n\n**必需字段**：\n- `sceneType`: \"location\"|\"dialogue\"|\"combat\"|\"system_message\"|\"puzzle\"\n- `sceneTitle`: string - 场景标题\n- `currentLocation`: string - 当前位置\n- `time`: string - 游戏时间\n- `narrative`: NarrativeEntry[] - 叙事内容数组\n- `playerChoices`: PlayerChoiceJSON[] - 玩家选项数组\n\n**可选字段**：\n- `variableUpdates`: VariableUpdateInstruction[] - 状态更新指令\n- `enemies`: EnemyStateJSON[] - 敌人/NPC列表\n- `combatLog`: string[] - 战斗日志\n- `sceneUuid`: string - 场景唯一标识符\n\n## NarrativeEntry结构\n- `type`: \"description\"|\"dialogue\"|\"systemMessage\"|\"actionDescription\"|\"thought\"\n- `content`: string - 文本内容（支持\\\\n换行）\n- `speaker`: string - 说话者（仅dialogue类型）\n- `actor`: string - 行动者（仅actionDescription类型）\n- `emotion`: string - 情绪描述\n\n## PlayerChoiceJSON结构\n- `id`: string - 选项ID（通常\"A\", \"B\", \"C\"）\n- `text`: string - 显示文本（可含检定信息）\n- `actionCommand`: string - 内部命令标识符",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 98,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 2,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 2,
      "displayIndex": 2,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 2,
        "probability": 100,
        "useProbability": true,
        "depth": 2,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    },
    "3": {
      "key": ["检定格式", "攻击检定", "技能检定", "豁免检定"],
      "keysecondary": ["DC", "检定信息", "格式规范"],
      "comment": "检定与交互格式 - 深度2",
      "content": "# ⚔️ 检定与交互格式规范\n\n## 检定信息嵌入格式\n\n**技能检定**：\n- 格式：`[DC{数值} {属性}({技能})]`\n- 示例：`\"尝试撬锁[DC15 敏捷(巧手)]\"`\n- 示例：`\"说服守卫[DC12 魅力(游说)]\"`\n\n**攻击检定**：\n- 格式：`[攻击 {目标} 使用 {武器} DC{AC}]`\n- 示例：`\"用长剑攻击地精[攻击 地精A 使用 长剑 DC13]\"`\n\n**法术攻击**：\n- 格式：`[法术攻击 {目标} 使用 {法术} DC{AC}]`\n- 示例：`\"对地精施放火焰箭[法术攻击 地精A 使用 火焰箭 DC13]\"`\n\n**法术豁免**：\n- 格式：`[法术豁免 {法术} DC{数值} {属性}]`\n- 示例：`\"施放魅惑人类[法术豁免 魅惑人类 DC14 感知]\"`\n\n## 客户端反馈处理\n客户端会返回详细的检定结果，AI必须基于这些结果推进剧情：\n- 成功/失败状态\n- 具体投骰数值\n- 伤害计算结果\n- 特殊效果触发\n\n**重要**：AI不需要重复计算，只需根据客户端反馈描述结果！",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 97,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 2,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 3,
      "displayIndex": 3,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 3,
        "probability": 100,
        "useProbability": true,
        "depth": 2,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    },
    "4": {
      "key": ["标准示例", "JSON模板", "场景示例"],
      "keysecondary": ["template", "example", "模板"],
      "comment": "标准JSON示例 - 深度3",
      "content": "# 📝 标准JSON输出示例\n\n## 对话场景示例\n```json\n{\n  \"sceneType\": \"dialogue\",\n  \"sceneTitle\": \"村长的请求\",\n  \"currentLocation\": \"碧水村村长小屋\",\n  \"time\": \"第一天 黄昏\",\n  \"narrative\": [\n    {\n      \"type\": \"description\",\n      \"content\": \"村长埃尔文的家中陈设简陋，但十分整洁。\"\n    },\n    {\n      \"type\": \"dialogue\",\n      \"speaker\": \"埃尔文村长\",\n      \"content\": \"勇敢的旅人，我们村子需要帮助。\",\n      \"emotion\": \"忧虑而恳切\"\n    }\n  ],\n  \"playerChoices\": [\n    {\n      \"id\": \"A\",\n      \"text\": \"我会尽力帮助。请详细说明情况。\",\n      \"actionCommand\": \"agree_help_ask_details\"\n    },\n    {\n      \"id\": \"B\",\n      \"text\": \"这很危险。你们能提供什么报酬？[DC12 魅力(游说)]\",\n      \"actionCommand\": \"negotiate_reward\"\n    }\n  ]\n}\n```\n\n## 战斗场景示例\n```json\n{\n  \"sceneType\": \"combat\",\n  \"sceneTitle\": \"遭遇森林狼\",\n  \"currentLocation\": \"幽暗森林小径\",\n  \"time\": \"第二天 清晨\",\n  \"narrative\": [\n    {\n      \"type\": \"description\",\n      \"content\": \"两只饥饿的森林狼从灌木丛中窜出！\"\n    }\n  ],\n  \"enemies\": [\n    {\n      \"id\": \"wolf_1\",\n      \"name\": \"森林狼 Alpha\",\n      \"hp\": { \"current\": 15, \"max\": 15 },\n      \"ac\": 13,\n      \"intent\": \"扑向你，试图撕咬！\"\n    }\n  ],\n  \"playerChoices\": [\n    {\n      \"id\": \"A\",\n      \"text\": \"用长剑攻击森林狼[攻击 森林狼 Alpha 使用 长剑 DC13]\",\n      \"actionCommand\": \"attack_wolf_longsword\"\n    }\n  ]\n}\n```",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 96,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 3,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 4,
      "displayIndex": 4,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 4,
        "probability": 100,
        "useProbability": true,
        "depth": 3,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    },
    "5": {
      "key": ["法术系统", "法术目标", "enemies数组"],
      "keysecondary": ["spell_casting", "target_selection", "NPC"],
      "comment": "法术系统特殊要求 - 深度3",
      "content": "# 🔮 法术系统特殊要求\n\n## 法术目标识别规则\n\n**重要**：任何可能成为法术目标的角色都必须在 `enemies` 数组中列出，无论其是否为敌对角色。\n\n### 必须包含的目标类型：\n- 受伤需要治疗的友方角色\n- 可以被魅惑、命令、祝福等法术影响的中立角色\n- 可以被攻击法术瞄准的敌对角色\n- 商人、守卫、村民等可能成为法术作用对象的NPC\n\n### EnemyStateJSON结构（用于所有可施法目标）：\n```json\n{\n  \"id\": \"unique_identifier\",\n  \"name\": \"显示名称\",\n  \"hp\": { \"current\": 3, \"max\": 8 },\n  \"ac\": 10,\n  \"statusEffects\": [\"重伤\", \"恐惧\"],\n  \"intent\": \"需要医疗救助\",\n  \"relationship\": \"友好\"\n}\n```\n\n### 法术施放选项示例：\n```json\n{\n  \"id\": \"A\",\n  \"text\": \"对她施放治疗真言法术[消耗1环法术槽]\",\n  \"actionCommand\": \"cast_cure_wounds_on_girl\"\n}\n```\n\n**记住**：客户端使用这些数据来：\n1. 显示可选目标列表\n2. 验证法术目标有效性\n3. 计算法术效果和伤害\n4. 更新目标状态",
      "constant": true,
      "vectorized": false,
      "selective": true,
      "selectiveLogic": 0,
      "addMemo": true,
      "order": 95,
      "position": 4,
      "disable": false,
      "excludeRecursion": false,
      "preventRecursion": false,
      "delayUntilRecursion": false,
      "probability": 100,
      "useProbability": true,
      "depth": 3,
      "group": "",
      "groupOverride": false,
      "groupWeight": 100,
      "scanDepth": null,
      "caseSensitive": null,
      "matchWholeWords": null,
      "useGroupScoring": false,
      "automationId": "",
      "role": 0,
      "sticky": 0,
      "cooldown": 0,
      "delay": 0,
      "uid": 5,
      "displayIndex": 5,
      "extensions": {
        "position": 4,
        "exclude_recursion": false,
        "display_index": 5,
        "probability": 100,
        "useProbability": true,
        "depth": 3,
        "selectiveLogic": 0,
        "group": "",
        "group_override": false,
        "group_weight": 100,
        "prevent_recursion": false,
        "delay_until_recursion": false,
        "scan_depth": null,
        "match_whole_words": null,
        "use_group_scoring": false,
        "case_sensitive": null,
        "automation_id": "",
        "role": 0,
        "vectorized": false,
        "sticky": 0,
        "cooldown": 0,
        "delay": 0
      }
    }
  }
}
